@echo off
echo Probando scraper avanzado con extraccion de PDF...

call venv\Scripts\activate.bat
python pdf_qr_scraper_advanced.py

echo.
echo Verificando resultados del scraping + PDF...
if exist "resultados_scraping_sat.xlsx" (
    echo ✓ Archivo con datos de PDF y scraping generado!
    echo ¿Abrir archivo de resultados? (S/N)
    set /p open_file="Respuesta: "
    if /i "%open_file%"=="S" (
        start "" "resultados_scraping_sat.xlsx"
    )
) else (
    echo ✗ No se generó el archivo de resultados
)

pause
