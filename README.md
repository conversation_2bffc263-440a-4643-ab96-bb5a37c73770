# Extractor de Códigos QR de PDFs

Este script automatiza el proceso de:
1. Extraer códigos QR de la primera página de archivos PDF
2. Visitar las URLs contenidas en los códigos QR
3. Extraer información tabular de esas páginas web
4. Exportar toda la información a un archivo Excel

## Requisitos Previos

### 1. Instalar Python
- Descarga Python desde: https://www.python.org/downloads/
- **IMPORTANTE**: Durante la instalación, marca la casilla "Add Python to PATH"
- Versión recomendada: Python 3.8 o superior

### 2. Instalar dependencias adicionales (Windows)
Para el reconocimiento de códigos QR, necesitas instalar Visual C++ Build Tools:
- Descarga desde: https://visualstudio.microsoft.com/visual-cpp-build-tools/
- O instala Visual Studio Community con las herramientas de C++

## Instalación

### Opción 1: Con Entorno Virtual (Recomendada) 🌟
1. Haz doble clic en `setup_venv.bat`
2. Espera a que termine la configuración
3. El script creará un entorno virtual aislado e instalará todas las dependencias

### Opción 2: Instalación automática (Sistema global)
1. Haz doble clic en `install_dependencies.bat`
2. Espera a que termine la instalación

### Opción 3: Instalación manual
Abre una terminal (cmd o PowerShell) en esta carpeta y ejecuta:
```bash
# Con entorno virtual (recomendado)
python -m venv venv
venv\Scripts\activate
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

# O instalación global
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
```

## Uso

### Opción 1: Con Entorno Virtual (Recomendada) 🌟
1. Coloca todos tus archivos PDF en la misma carpeta que el script
2. Haz doble clic en `run_project.bat`
3. El script activará automáticamente el entorno virtual y ejecutará el procesamiento

### Opción 2: Activación manual del entorno virtual
1. Haz doble clic en `activate_venv.bat`
2. En la terminal que se abre, ejecuta: `python pdf_qr_extractor.py`

### Opción 3: Ejecución directa (si instalaste globalmente)
1. Coloca todos tus archivos PDF en la misma carpeta que el script
2. Haz doble clic en `pdf_qr_extractor.py` o ejecuta desde terminal:
   ```bash
   python pdf_qr_extractor.py
   ```

### El script procesará automáticamente:
- Todos los archivos `.pdf` en la carpeta actual
- Extraerá códigos QR de la primera página de cada PDF
- Visitará las URLs encontradas
- Extraerá tablas y datos estructurados
- Generará un archivo Excel con los resultados

## Resultados

El script genera un archivo `resultados_qr_extraction.xlsx` con:

### Hoja "Resumen"
- Lista de todos los PDFs procesados
- URLs extraídas
- Títulos de las páginas web
- Errores (si los hay)

### Hojas individuales
- Una hoja por cada PDF con datos exitosamente extraídos
- Tablas organizadas y formateadas
- Datos estructurados cuando no hay tablas

## Solución de Problemas

### Error: "Python no encontrado"
- Reinstala Python marcando "Add Python to PATH"
- Reinicia la terminal/computadora después de instalar

### Error: "No se encontró código QR"
- Verifica que el PDF tenga un código QR visible en la primera página
- El código QR debe estar bien definido y no borroso

### Error: "Error de conexión"
- Verifica tu conexión a internet
- Algunas URLs pueden requerir autenticación o estar bloqueadas

### Error: "Microsoft Visual C++ 14.0 is required"
- Instala Visual C++ Build Tools (ver requisitos previos)

## Características del Script

- **Detección automática**: Encuentra todos los PDFs en la carpeta
- **Alta resolución**: Procesa imágenes a 3x resolución para mejor detección de QR
- **Manejo de errores**: Continúa procesando aunque algunos archivos fallen
- **Exportación estructurada**: Organiza los datos en múltiples hojas de Excel
- **Respetuoso con servidores**: Pausa 1 segundo entre peticiones web
- **Logging detallado**: Muestra el progreso y errores en tiempo real

## Archivos del Proyecto

### Scripts de Configuración
- `setup_venv.bat`: Crea y configura el entorno virtual
- `activate_venv.bat`: Activa el entorno virtual manualmente
- `run_project.bat`: Ejecuta todo el proyecto con entorno virtual
- `install_dependencies.bat`: Instalación global (sin entorno virtual)

### Scripts Principales
- `pdf_qr_extractor.py`: Script principal del procesamiento
- `test_single_pdf.py`: Script de prueba para un solo PDF
- `requirements.txt`: Lista de dependencias

### Archivos Generados
- `venv/`: Carpeta del entorno virtual (se crea automáticamente)
- `resultados_qr_extraction.xlsx`: Archivo principal con todos los resultados
- Logs en consola durante la ejecución

## Limitaciones

- Solo procesa la primera página de cada PDF
- Solo extrae el primer código QR encontrado por página
- Requiere conexión a internet para acceder a las URLs
- Algunas páginas web pueden bloquear el acceso automatizado

## Soporte

Si encuentras problemas:
1. Verifica que todos los requisitos estén instalados
2. Revisa los mensajes de error en la consola
3. Asegúrate de que los PDFs contengan códigos QR válidos
