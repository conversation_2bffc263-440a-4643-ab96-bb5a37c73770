@echo off
echo ========================================
echo    CONFIGURACION DE ENTORNO VIRTUAL
echo ========================================
echo.

REM Buscar Python
set PYTHON_CMD=
echo Buscando Python...

REM Probar ubicaciones comunes
if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe" (
    set PYTHON_CMD=%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe
    goto :found
)

if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe" (
    set PYTHON_CMD=%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe
    goto :found
)

if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe" (
    set PYTHON_CMD=%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe
    goto :found
)

REM Buscar dinámicamente
for /d %%p in ("%USERPROFILE%\AppData\Local\Programs\Python\Python*") do (
    if exist "%%p\python.exe" (
        set PYTHON_CMD=%%p\python.exe
        goto :found
    )
)

REM Probar python en PATH (pero verificar que no sea WindowsApps)
where python >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('where python') do (
        echo %%i | findstr /v "WindowsApps" >nul
        if !errorlevel! equ 0 (
            set PYTHON_CMD=%%i
            goto :found
        )
    )
)

echo ERROR: No se pudo encontrar Python instalado.
echo.
echo Soluciones:
echo 1. Instala Python desde https://www.python.org/downloads/
echo 2. Asegurate de marcar "Add Python to PATH" durante la instalacion
echo.
pause
exit /b 1

:found
echo Python encontrado: %PYTHON_CMD%
"%PYTHON_CMD%" --version
echo.

REM Verificar si ya existe el entorno virtual
if exist "venv" (
    echo El entorno virtual ya existe.
    echo ¿Quieres recrearlo? (S/N)
    set /p recreate="Respuesta: "
    if /i "%recreate%"=="S" (
        echo Eliminando entorno virtual existente...
        rmdir /s /q venv
    ) else (
        echo Usando entorno virtual existente.
        goto :activate
    )
)

echo Creando entorno virtual...
"%PYTHON_CMD%" -m venv venv

if %errorlevel% neq 0 (
    echo ERROR: No se pudo crear el entorno virtual.
    echo Verifica que Python esté correctamente instalado.
    pause
    exit /b 1
)

echo ✓ Entorno virtual creado exitosamente.

:activate
echo.
echo Activando entorno virtual...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo ERROR: No se pudo activar el entorno virtual.
    pause
    exit /b 1
)

echo ✓ Entorno virtual activado.
echo.

echo Actualizando pip...
python -m pip install --upgrade pip

echo.
echo Instalando dependencias...
python -m pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo        CONFIGURACION COMPLETADA
    echo ========================================
    echo.
    echo ✓ Entorno virtual creado y configurado
    echo ✓ Todas las dependencias instaladas
    echo.
    echo Para usar el proyecto:
    echo 1. Ejecuta: activate_venv.bat
    echo 2. Luego: python pdf_qr_extractor.py
    echo.
    echo O simplemente ejecuta: run_project.bat
    echo.
) else (
    echo.
    echo ERROR: Hubo problemas instalando las dependencias.
    echo Revisa los mensajes de error arriba.
)

pause
