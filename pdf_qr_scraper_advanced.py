#!/usr/bin/env python3
"""
Script avanzado para hacer scraping de las URLs del SAT con múltiples estrategias
"""

import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
import io
import os
import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from datetime import datetime
import urllib3
import warnings
import ssl
import subprocess
import sys

class AdvancedSATScraper:
    def __init__(self):
        self.results = []
        self.qr_detector = cv2.QRCodeDetector()
        self.setup_ssl_bypass()

    def setup_ssl_bypass(self):
        """
        Configura múltiples estrategias para bypass SSL
        """
        # Deshabilitar warnings SSL
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        warnings.filterwarnings('ignore')
        
        # Configurar SSL context más permisivo
        try:
            ssl._create_default_https_context = ssl._create_unverified_context
        except:
            pass

    def extract_qr_from_pdf(self, pdf_path):
        """
        Extrae el código QR de la primera página de un PDF
        """
        try:
            print(f"Procesando: {pdf_path}")

            doc = fitz.open(pdf_path)
            page = doc[0]

            mat = fitz.Matrix(3, 3)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")

            img = Image.open(io.BytesIO(img_data))
            img_array = np.array(img)

            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            data, bbox, _ = self.qr_detector.detectAndDecode(gray)
            doc.close()

            if data:
                print(f"  ✓ QR encontrado: {data}")
                return data
            else:
                print(f"  ✗ No se encontró código QR")
                return None

        except Exception as e:
            print(f"  ✗ Error procesando {pdf_path}: {str(e)}")
            return None

    def extract_pdf_text_data(self, pdf_path):
        """
        Extrae datos directamente del texto del PDF
        """
        try:
            print(f"  📄 Extrayendo datos del PDF...")

            doc = fitz.open(pdf_path)

            # Extraer texto de todas las páginas
            full_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                full_text += text + "\n"

            doc.close()

            # Limpiar el texto (remover espacios extra y saltos de línea)
            clean_text = re.sub(r'\s+', ' ', full_text).strip()

            # Extraer datos específicos del PDF
            pdf_data = self.parse_pdf_text(clean_text)

            if pdf_data:
                print(f"    ✓ Extraídos {len(pdf_data)} campos del PDF")
                return pdf_data
            else:
                print(f"    ⚠ No se pudieron extraer datos del PDF")
                return {}

        except Exception as e:
            print(f"    ✗ Error extrayendo texto del PDF: {str(e)}")
            return {}

    def parse_pdf_text(self, text):
        """
        Parsea el texto del PDF para extraer datos estructurados
        """
        data = {}

        try:
            # Patrones específicos para el formato real del PDF del SAT
            patterns = {
                # Datos de Identificación del Contribuyente (formato real)
                'pdf_rfc': r'RFC:\s*([A-Z0-9]{12,13})',
                'pdf_curp': r'CURP:\s*([A-Z0-9]{18})',
                'pdf_nombre': r'Nombre\s*\(s\):\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Primer|$)',
                'pdf_primer_apellido': r'Primer Apellido:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Segundo|$)',
                'pdf_segundo_apellido': r'Segundo Apellido:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Fecha|$)',
                'pdf_fecha_inicio_operaciones': r'Fecha inicio de operaciones:\s*([0-9A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Estatus|$)',
                'pdf_estatus_padron': r'Estatus en el padrón:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Fecha|$)',
                'pdf_fecha_ultimo_cambio': r'Fecha de último cambio de estado:\s*([0-9A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Nombre|$)',
                'pdf_nombre_comercial': r'Nombre Comercial:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Datos|$)',

                # Datos del domicilio registrado (formato real)
                'pdf_codigo_postal': r'Código Postal:\s*(\d{5})',
                'pdf_tipo_vialidad': r'Tipo de Vialidad:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Nombre|$)',
                'pdf_nombre_vialidad': r'Nombre de Vialidad:\s*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?=\s*Número|$)',
                'pdf_numero_exterior': r'Número Exterior:\s*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?=\s*Número|$)',
                'pdf_numero_interior': r'Número Interior:\s*([A-ZÁÉÍÓÚÑ0-9\s]*?)(?=\s*Nombre|$)',
                'pdf_nombre_colonia': r'Nombre de la Colonia:\s*([A-ZÁÉÍÓÚÑ\s]*?)(?=\s*Nombre|$)',
                'pdf_nombre_localidad': r'Nombre de la Localidad:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Nombre|$)',
                'pdf_municipio': r'Nombre del Municipio o Demarcación Territorial:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Nombre|$)',
                'pdf_entidad_federativa': r'Nombre de la Entidad Federativa:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Entre|$)',
                'pdf_entre_calle': r'Entre Calle:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*$)',
            }

            extracted_count = 0
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    if value and len(value) > 1:
                        data[key] = value
                        extracted_count += 1
                        print(f"      ✓ {key}: {value}")

            # Patrones alternativos más flexibles si no se encontraron los principales
            if extracted_count < 8:
                print(f"      ⚠ Solo se encontraron {extracted_count} campos, probando patrones alternativos...")

                alt_patterns = {
                    'pdf_rfc_alt': r'([A-Z]{4}\d{6}[A-Z0-9]{3})',
                    'pdf_curp_alt': r'([A-Z]{4}\d{6}[HM][A-Z]{5}[0-9A-Z]\d)',
                    'pdf_nombre_alt': r'(?:Nombre|NOMBRE)[^:]*:?\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*(?:Primer|PRIMER|Apellido|APELLIDO))',
                    'pdf_primer_apellido_alt': r'(?:Primer Apellido|PRIMER APELLIDO)[^:]*:?\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*(?:Segundo|SEGUNDO))',
                    'pdf_segundo_apellido_alt': r'(?:Segundo Apellido|SEGUNDO APELLIDO)[^:]*:?\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*(?:Fecha|FECHA))',
                    'pdf_codigo_postal_alt': r'(?:Código Postal|CÓDIGO POSTAL)[^:]*:?\s*(\d{5})',
                    'pdf_entidad_federativa_alt': r'(?:Entidad Federativa|ENTIDAD FEDERATIVA)[^:]*:?\s*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*(?:Entre|ENTRE|$))',
                }

                for key, pattern in alt_patterns.items():
                    base_key = key.replace('_alt', '')
                    if base_key not in data:
                        match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                        if match:
                            value = match.group(1).strip()
                            if value and len(value) > 1:
                                data[base_key] = value
                                extracted_count += 1
                                print(f"      ✓ {base_key} (alternativo): {value}")

            print(f"    ✓ Total extraído del PDF: {extracted_count} campos")

        except Exception as e:
            print(f"      ⚠ Error parseando texto del PDF: {str(e)}")

        return data

    def scrape_sat_url_strategy1(self, url):
        """
        Estrategia 1: Requests con configuración SSL personalizada
        """
        try:
            print(f"    Estrategia 1: Requests con SSL bypass...")
            
            session = requests.Session()
            session.verify = False
            
            # Headers más completos
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'es-MX,es;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            })
            
            # Configurar adaptador con SSL personalizado
            from requests.adapters import HTTPAdapter
            from urllib3.util.ssl_ import create_urllib3_context
            
            class SSLAdapter(HTTPAdapter):
                def init_poolmanager(self, *args, **kwargs):
                    context = create_urllib3_context()
                    context.set_ciphers('DEFAULT:@SECLEVEL=1')
                    kwargs['ssl_context'] = context
                    return super().init_poolmanager(*args, **kwargs)
            
            session.mount('https://', SSLAdapter())
            
            response = session.get(url, timeout=30)
            
            if response.status_code == 200:
                print(f"      ✓ Estrategia 1 exitosa!")
                return response.text
            else:
                print(f"      ✗ Código de estado: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"      ✗ Estrategia 1 falló: {str(e)}")
            return None

    def scrape_sat_url_strategy2(self, url):
        """
        Estrategia 2: Usar curl como subprocess
        """
        try:
            print(f"    Estrategia 2: Usando curl...")
            
            curl_command = [
                'curl',
                '-k',  # Ignorar errores SSL
                '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                '--connect-timeout', '30',
                '--max-time', '60',
                '--location',  # Seguir redirects
                '--compressed',
                url
            ]
            
            result = subprocess.run(curl_command, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and result.stdout:
                print(f"      ✓ Estrategia 2 exitosa!")
                return result.stdout
            else:
                print(f"      ✗ Curl falló: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"      ✗ Estrategia 2 falló: {str(e)}")
            return None

    def scrape_sat_url_strategy3(self, url):
        """
        Estrategia 3: Usar urllib con SSL context personalizado
        """
        try:
            print(f"    Estrategia 3: urllib con SSL personalizado...")
            
            import urllib.request
            import urllib.parse
            
            # Crear contexto SSL personalizado
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            context.set_ciphers('DEFAULT:@SECLEVEL=1')
            
            # Crear request con headers
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.add_header('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8')
            
            with urllib.request.urlopen(req, context=context, timeout=30) as response:
                content = response.read().decode('utf-8')
                print(f"      ✓ Estrategia 3 exitosa!")
                return content
                
        except Exception as e:
            print(f"      ✗ Estrategia 3 falló: {str(e)}")
            return None

    def scrape_sat_url_strategy4(self, url):
        """
        Estrategia 4: Usar requests con configuración legacy SSL
        """
        try:
            print(f"    Estrategia 4: Requests con SSL legacy...")
            
            import requests.packages.urllib3.util.ssl_
            
            # Forzar uso de TLS 1.0/1.1
            requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS += ':HIGH:!DH:!aNULL'
            
            session = requests.Session()
            session.verify = False
            
            # Configurar headers específicos para SAT
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'es-mx',
                'Connection': 'keep-alive'
            })
            
            response = session.get(url, timeout=30)
            
            if response.status_code == 200:
                print(f"      ✓ Estrategia 4 exitosa!")
                return response.text
            else:
                print(f"      ✗ Código de estado: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"      ✗ Estrategia 4 falló: {str(e)}")
            return None

    def install_curl_if_needed(self):
        """
        Verifica si curl está disponible e intenta instalarlo
        """
        try:
            subprocess.run(['curl', '--version'], capture_output=True, check=True)
            return True
        except:
            print("    ⚠ curl no está disponible, saltando estrategia 2")
            return False

    def scrape_sat_data(self, url, pdf_filename):
        """
        Intenta hacer scraping de la URL del SAT usando múltiples estrategias
        """
        print(f"  🌐 Haciendo scraping de URL del SAT...")
        
        # Extraer información básica de la URL
        sat_data = {
            'archivo_pdf': pdf_filename,
            'url': url,
            'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Extraer RFC de la URL
        rfc_match = re.search(r'D3=(\d+)_([A-Z0-9]+)', url)
        if rfc_match:
            sat_data['numero_registro'] = rfc_match.group(1)
            sat_data['rfc'] = rfc_match.group(2)
        
        # Intentar scraping con múltiples estrategias
        html_content = None
        
        # Estrategia 1: Requests con SSL bypass
        if not html_content:
            html_content = self.scrape_sat_url_strategy1(url)
        
        # Estrategia 2: curl (si está disponible)
        if not html_content and self.install_curl_if_needed():
            html_content = self.scrape_sat_url_strategy2(url)
        
        # Estrategia 3: urllib con SSL personalizado
        if not html_content:
            html_content = self.scrape_sat_url_strategy3(url)
        
        # Estrategia 4: Requests con SSL legacy
        if not html_content:
            html_content = self.scrape_sat_url_strategy4(url)
        
        # Si obtuvimos contenido, parsearlo
        if html_content:
            print(f"    ✓ Contenido obtenido, parseando datos...")
            parsed_data = self.parse_sat_content(html_content)
            sat_data.update(parsed_data)
            sat_data['scraping_exitoso'] = True
        else:
            print(f"    ✗ No se pudo obtener contenido de ninguna estrategia")
            sat_data['scraping_exitoso'] = False
            sat_data['error'] = 'No se pudo acceder al contenido con ninguna estrategia'
        
        return sat_data

    def parse_sat_content(self, html_content):
        """
        Parsea el contenido HTML del SAT para extraer datos estructurados
        """
        data = {}
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            text = soup.get_text()
            
            # Patrones específicos para datos del SAT
            patterns = {
                'curp': r'CURP[:\s]*([A-Z0-9]{18})',
                'nombre': r'Nombre[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Apellido|\s*\n)',
                'apellido_paterno': r'Apellido Paterno[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Apellido|\s*\n)',
                'apellido_materno': r'Apellido Materno[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Fecha|\s*\n)',
                'fecha_nacimiento': r'Fecha Nacimiento[:\s]*(\d{2}-\d{2}-\d{4})',
                'fecha_inicio_operaciones': r'Fecha de Inicio de operaciones[:\s]*(\d{2}-\d{2}-\d{4})',
                'situacion_contribuyente': r'Situación del contribuyente[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Fecha|\s*\n)',
                'fecha_ultimo_cambio': r'Fecha del último cambio de situación[:\s]*(\d{2}-\d{2}-\d{4})',
                'entidad_federativa': r'Entidad Federativa[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Municipio|\s*\n)',
                'municipio': r'Municipio o delegación[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Colonia|\s*\n)',
                'colonia': r'Colonia[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Tipo|\s*\n)',
                'tipo_vialidad': r'Tipo de vialidad[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Nombre|\s*\n)',
                'nombre_vialidad': r'Nombre de la vialidad[:\s]*([A-ZÁÉÍÓÚÑ\s0-9]+?)(?=\s*Número|\s*\n)',
                'numero_exterior': r'Número exterior[:\s]*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?=\s*Número|\s*CP|\s*\n)',
                'numero_interior': r'Número interior[:\s]*([A-ZÁÉÍÓÚÑ0-9\s]*?)(?=\s*CP|\s*\n)',
                'cp': r'CP[:\s]*(\d{5})',
                'correo_electronico': r'Correo electrónico[:\s]*([A-Za-z0-9@._-]+)',
                'al': r'AL[:\s]*([A-ZÁÉÍÓÚÑ\s0-9]+?)(?=\s*Régimen|\s*\n)',
                'regimen': r'Régimen[:\s]*([^\\n]+?)(?=\s*Fecha|\s*\n)',
                'fecha_alta': r'Fecha de alta[:\s]*(\d{2}-\d{2}-\d{4})',
            }
            
            extracted_count = 0
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    if value and len(value) > 1 and value != ':':
                        data[key] = value
                        extracted_count += 1
                        print(f"      ✓ {key}: {value}")
            
            print(f"    ✓ Extraídos {extracted_count} campos de datos")
            
            # Buscar tablas adicionales
            tables = soup.find_all('table')
            if tables:
                print(f"    ✓ Encontradas {len(tables)} tabla(s)")
                for i, table in enumerate(tables):
                    table_data = self.parse_table(table)
                    if table_data:
                        data[f'tabla_{i+1}'] = table_data
            
        except Exception as e:
            print(f"      ⚠ Error parseando contenido: {str(e)}")
        
        return data

    def parse_table(self, table):
        """
        Parsea una tabla HTML
        """
        try:
            rows = []
            for row in table.find_all('tr'):
                cells = []
                for cell in row.find_all(['td', 'th']):
                    text = cell.get_text(strip=True)
                    if text:
                        cells.append(text)
                
                if cells:
                    rows.append(cells)
            
            return rows if rows else None
            
        except Exception:
            return None

    def process_all_pdfs(self, directory='.'):
        """
        Procesa todos los PDFs en el directorio
        """
        print("🚀 SCRAPER AVANZADO DE URLs DEL SAT + EXTRACCIÓN DE PDF")
        print("=" * 60)
        print("Extrayendo QRs, haciendo scraping web Y extrayendo datos del PDF...")
        
        pdf_files = [f for f in os.listdir(directory) if f.endswith('.pdf')]
        
        if not pdf_files:
            print("No se encontraron archivos PDF")
            return
        
        print(f"Encontrados {len(pdf_files)} archivos PDF\n")
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"[{i}/{len(pdf_files)}] 📄 {pdf_file}")

            # Extraer QR
            url = self.extract_qr_from_pdf(pdf_file)

            # Extraer datos del PDF
            pdf_data = self.extract_pdf_text_data(pdf_file)

            if url:
                # Hacer scraping de la URL
                data = self.scrape_sat_data(url, pdf_file)

                # Combinar datos del PDF con datos del scraping
                data.update(pdf_data)
                data['extraccion_pdf_exitosa'] = len(pdf_data) > 0

                self.results.append(data)

                # Pausa entre peticiones
                time.sleep(3)
            else:
                # Si no hay URL, al menos guardar datos del PDF
                result_data = {
                    'archivo_pdf': pdf_file,
                    'url': 'No encontrada',
                    'error': 'No se pudo extraer código QR',
                    'scraping_exitoso': False,
                    'extraccion_pdf_exitosa': len(pdf_data) > 0,
                    'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                result_data.update(pdf_data)
                self.results.append(result_data)

            print()

    def export_to_excel(self, filename='resultados_scraping_sat.xlsx'):
        """
        Exporta los resultados del scraping a Excel
        """
        if not self.results:
            print("No hay resultados para exportar")
            return
        
        print(f"📊 Exportando resultados a {filename}")
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # Hoja 1: Resumen de scraping
                summary_data = []
                for result in self.results:
                    nombre_completo = ""
                    if result.get('nombre') and result.get('apellido_paterno'):
                        nombre_completo = f"{result.get('nombre', '')} {result.get('apellido_paterno', '')} {result.get('apellido_materno', '')}".strip()
                    
                    summary_row = {
                        'Archivo PDF': result.get('archivo_pdf', ''),
                        'RFC': result.get('rfc', ''),
                        'Scraping Exitoso': 'SÍ' if result.get('scraping_exitoso') else 'NO',
                        'Nombre Completo': nombre_completo,
                        'CURP': result.get('curp', ''),
                        'Situación': result.get('situacion_contribuyente', ''),
                        'Municipio': result.get('municipio', ''),
                        'Estado': result.get('entidad_federativa', ''),
                        'Error': result.get('error', ''),
                        'URL': result.get('url', '')
                    }
                    summary_data.append(summary_row)
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Resumen Scraping', index=False)
                
                # Hoja 2: Datos completos extraídos
                detailed_data = []
                for result in self.results:
                    if result.get('scraping_exitoso'):
                        detailed_row = {
                            'Archivo PDF': result.get('archivo_pdf', ''),
                            'RFC': result.get('rfc', ''),
                            'CURP': result.get('curp', ''),
                            'Nombre': result.get('nombre', ''),
                            'Apellido Paterno': result.get('apellido_paterno', ''),
                            'Apellido Materno': result.get('apellido_materno', ''),
                            'Fecha Nacimiento': result.get('fecha_nacimiento', ''),
                            'Fecha Inicio Operaciones': result.get('fecha_inicio_operaciones', ''),
                            'Situación Contribuyente': result.get('situacion_contribuyente', ''),
                            'Fecha Último Cambio': result.get('fecha_ultimo_cambio', ''),
                            'Entidad Federativa': result.get('entidad_federativa', ''),
                            'Municipio': result.get('municipio', ''),
                            'Colonia': result.get('colonia', ''),
                            'Tipo Vialidad': result.get('tipo_vialidad', ''),
                            'Nombre Vialidad': result.get('nombre_vialidad', ''),
                            'Número Exterior': result.get('numero_exterior', ''),
                            'Número Interior': result.get('numero_interior', ''),
                            'CP': result.get('cp', ''),
                            'Correo Electrónico': result.get('correo_electronico', ''),
                            'AL': result.get('al', ''),
                            'Régimen': result.get('regimen', ''),
                            'Fecha Alta': result.get('fecha_alta', ''),
                            'URL Original': result.get('url', '')
                        }
                        detailed_data.append(detailed_row)
                
                if detailed_data:
                    df_detailed = pd.DataFrame(detailed_data)
                    df_detailed.to_excel(writer, sheet_name='Datos Extraídos', index=False)

                # Hoja 3: Datos del PDF
                pdf_data = []
                for result in self.results:
                    if result.get('extraccion_pdf_exitosa'):
                        pdf_row = {
                            'Archivo PDF': result.get('archivo_pdf', ''),
                            'RFC (PDF)': result.get('pdf_rfc', result.get('pdf_rfc_alt', '')),
                            'CURP (PDF)': result.get('pdf_curp', result.get('pdf_curp_alt', '')),
                            'Nombre(s)': result.get('pdf_nombre', result.get('pdf_nombre_alt', '')),
                            'Primer Apellido': result.get('pdf_primer_apellido', result.get('pdf_apellido_alt', '')),
                            'Segundo Apellido': result.get('pdf_segundo_apellido', ''),
                            'Fecha Inicio Operaciones': result.get('pdf_fecha_inicio_operaciones', ''),
                            'Estatus en el Padrón': result.get('pdf_estatus_padron', ''),
                            'Fecha Último Cambio Estado': result.get('pdf_fecha_ultimo_cambio', ''),
                            'Nombre Comercial': result.get('pdf_nombre_comercial', ''),
                            'Código Postal': result.get('pdf_codigo_postal', ''),
                            'Tipo de Vialidad': result.get('pdf_tipo_vialidad', ''),
                            'Nombre de Vialidad': result.get('pdf_nombre_vialidad', ''),
                            'Número Exterior': result.get('pdf_numero_exterior', ''),
                            'Número Interior': result.get('pdf_numero_interior', ''),
                            'Nombre de la Colonia': result.get('pdf_nombre_colonia', ''),
                            'Nombre de la Localidad': result.get('pdf_nombre_localidad', ''),
                            'Municipio o Demarcación': result.get('pdf_municipio', ''),
                            'Entidad Federativa': result.get('pdf_entidad_federativa', ''),
                            'Entre Calle': result.get('pdf_entre_calle', ''),
                        }
                        pdf_data.append(pdf_row)

                if pdf_data:
                    df_pdf = pd.DataFrame(pdf_data)
                    df_pdf.to_excel(writer, sheet_name='Datos del PDF', index=False)

                # Hoja 4: Estadísticas
                successful_scraping = len([r for r in self.results if r.get('scraping_exitoso')])
                successful_pdf_extraction = len([r for r in self.results if r.get('extraccion_pdf_exitosa')])
                total_files = len(self.results)

                stats_data = [
                    ['Métrica', 'Valor'],
                    ['Total PDFs procesados', total_files],
                    ['Scraping web exitoso', successful_scraping],
                    ['Extracción PDF exitosa', successful_pdf_extraction],
                    ['Scraping web fallido', total_files - successful_scraping],
                    ['Extracción PDF fallida', total_files - successful_pdf_extraction],
                    ['Tasa éxito scraping web', f"{(successful_scraping/total_files*100):.1f}%" if total_files > 0 else "0%"],
                    ['Tasa éxito extracción PDF', f"{(successful_pdf_extraction/total_files*100):.1f}%" if total_files > 0 else "0%"],
                    ['Fecha procesamiento', datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
                ]

                df_stats = pd.DataFrame(stats_data[1:], columns=stats_data[0])
                df_stats.to_excel(writer, sheet_name='Estadísticas', index=False)
            
            print(f"✓ Archivo Excel generado: {filename}")
            print(f"✓ Scraping web exitoso: {successful_scraping}/{total_files}")
            print(f"✓ Extracción PDF exitosa: {successful_pdf_extraction}/{total_files}")

        except Exception as e:
            print(f"✗ Error exportando: {str(e)}")

def main():
    """
    Función principal
    """
    print("🌐 SCRAPER AVANZADO DE URLs DEL SAT + EXTRACCIÓN DE PDF")
    print("Extrae datos de URLs web Y del contenido del PDF")
    print("=" * 60)

    scraper = AdvancedSATScraper()

    # Procesar todos los PDFs
    scraper.process_all_pdfs()

    # Exportar resultados
    scraper.export_to_excel()

    print("\n" + "=" * 60)
    print("🎉 PROCESAMIENTO COMPLETADO")
    print("📁 Revisa el archivo Excel con 4 hojas de datos:")
    print("   1. Resumen Scraping - Estado general")
    print("   2. Datos Extraídos - Datos del scraping web")
    print("   3. Datos del PDF - Datos extraídos del PDF")
    print("   4. Estadísticas - Métricas del proceso")

if __name__ == "__main__":
    main()
