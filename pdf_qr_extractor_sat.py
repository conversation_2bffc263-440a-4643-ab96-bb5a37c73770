#!/usr/bin/env python3
"""
Script especializado para extraer códigos QR de PDFs del SAT y obtener datos estructurados
"""

import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
import io
import os
import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from datetime import datetime
import ssl
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import warnings

class SATQRProcessor:
    def __init__(self):
        self.results = []
        self.setup_session()
        
        # Inicializar detector QR de OpenCV
        self.qr_detector = cv2.QRCodeDetector()

    def setup_session(self):
        """
        Configura la sesión HTTP con configuraciones especiales para el SAT
        """
        self.session = requests.Session()
        
        # Headers más completos para simular un navegador real
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Deshabilitar verificación SSL y warnings
        self.session.verify = False
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        warnings.filterwarnings('ignore', message='Unverified HTTPS request')
        
        # Configurar reintentos
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # Crear adaptador personalizado
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Configurar timeout
        self.session.timeout = 30

    def extract_qr_from_pdf(self, pdf_path):
        """
        Extrae el código QR de la primera página de un PDF usando OpenCV
        """
        try:
            print(f"Procesando: {pdf_path}")
            
            # Abrir el PDF
            doc = fitz.open(pdf_path)
            
            # Obtener la primera página
            page = doc[0]
            
            # Convertir la página a imagen con alta resolución
            mat = fitz.Matrix(3, 3)  # Escalar para mejor resolución
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # Convertir a imagen PIL
            img = Image.open(io.BytesIO(img_data))
            
            # Convertir a array numpy para OpenCV
            img_array = np.array(img)
            
            # Convertir a escala de grises si es necesario
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # Detectar códigos QR usando OpenCV
            data, bbox, _ = self.qr_detector.detectAndDecode(gray)
            
            doc.close()
            
            if data:
                print(f"  ✓ QR encontrado: {data}")
                return data
            else:
                print(f"  ✗ No se encontró código QR")
                return None
                
        except Exception as e:
            print(f"  ✗ Error procesando {pdf_path}: {str(e)}")
            return None

    def extract_sat_data(self, url, pdf_filename):
        """
        Extrae información específica del SAT usando múltiples estrategias
        """
        print(f"  Accediendo a URL del SAT: {url}")
        
        # Estrategia 1: Intentar con configuraciones SSL flexibles
        try:
            response = self.attempt_sat_connection(url)
            if response:
                return self.parse_sat_response(response, url, pdf_filename)
        except Exception as e:
            print(f"    ⚠ Estrategia 1 falló: {str(e)}")
        
        # Estrategia 2: Usar requests con configuración SSL personalizada
        try:
            response = self.attempt_with_custom_ssl(url)
            if response:
                return self.parse_sat_response(response, url, pdf_filename)
        except Exception as e:
            print(f"    ⚠ Estrategia 2 falló: {str(e)}")
        
        # Si todas las estrategias fallan, extraer información de la URL
        return self.extract_info_from_url(url, pdf_filename)

    def attempt_sat_connection(self, url):
        """
        Intenta conectar al SAT con configuraciones especiales
        """
        try:
            # Configurar contexto SSL más permisivo
            import ssl
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            context.set_ciphers('DEFAULT:@SECLEVEL=1')
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return response
            
        except Exception as e:
            print(f"      ✗ Error de conexión SAT: {str(e)}")
            return None

    def attempt_with_custom_ssl(self, url):
        """
        Intenta con configuración SSL personalizada
        """
        try:
            # Crear nueva sesión con configuraciones específicas
            import requests
            from requests.packages.urllib3.exceptions import InsecureRequestWarning
            requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
            
            session = requests.Session()
            session.verify = False
            
            response = session.get(url, timeout=30, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                return response
            else:
                print(f"      ✗ Código de estado: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"      ✗ Error SSL personalizado: {str(e)}")
            return None

    def parse_sat_response(self, response, url, pdf_filename):
        """
        Parsea la respuesta del SAT y extrae datos estructurados
        """
        try:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Buscar datos específicos del SAT
            sat_data = {
                'archivo_pdf': pdf_filename,
                'url': url,
                'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Extraer RFC de la URL
            rfc_match = re.search(r'D3=\d+_([A-Z0-9]+)', url)
            if rfc_match:
                sat_data['rfc'] = rfc_match.group(1)
            
            # Buscar tablas de datos
            tables = soup.find_all('table')
            if tables:
                print(f"    ✓ Encontradas {len(tables)} tabla(s)")
                for i, table in enumerate(tables):
                    table_data = self.parse_sat_table(table)
                    if table_data:
                        sat_data[f'tabla_{i+1}'] = table_data
            
            # Buscar datos específicos del SAT
            sat_data.update(self.extract_sat_specific_data(soup))
            
            return sat_data
            
        except Exception as e:
            print(f"    ✗ Error parseando respuesta SAT: {str(e)}")
            return self.extract_info_from_url(url, pdf_filename)

    def extract_sat_specific_data(self, soup):
        """
        Extrae datos específicos del formato del SAT
        """
        data = {}
        
        try:
            # Buscar patrones específicos del SAT
            text = soup.get_text()
            
            # Extraer datos de identificación
            patterns = {
                'curp': r'CURP[:\s]+([A-Z0-9]{18})',
                'nombre': r'Nombre[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'apellido_paterno': r'Apellido Paterno[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'apellido_materno': r'Apellido Materno[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'fecha_nacimiento': r'Fecha Nacimiento[:\s]+(\d{2}-\d{2}-\d{4})',
                'fecha_inicio_operaciones': r'Fecha de Inicio de operaciones[:\s]+(\d{2}-\d{2}-\d{4})',
                'situacion_contribuyente': r'Situación del contribuyente[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'entidad_federativa': r'Entidad Federativa[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'municipio': r'Municipio o delegación[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'colonia': r'Colonia[:\s]+([A-ZÁÉÍÓÚÑ\s]+)',
                'cp': r'CP[:\s]+(\d{5})',
                'regimen': r'Régimen[:\s]+([A-ZÁÉÍÓÚÑ\s,]+)',
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    data[key] = match.group(1).strip()
            
        except Exception as e:
            print(f"      ⚠ Error extrayendo datos específicos: {str(e)}")
        
        return data

    def parse_sat_table(self, table):
        """
        Parsea tablas específicas del SAT
        """
        try:
            rows = []
            for row in table.find_all('tr'):
                cells = []
                for cell in row.find_all(['td', 'th']):
                    text = cell.get_text(strip=True)
                    cells.append(text)
                
                if cells and any(cell.strip() for cell in cells):
                    rows.append(cells)
            
            return rows if rows else None
            
        except Exception as e:
            print(f"      ✗ Error parseando tabla SAT: {str(e)}")
            return None

    def extract_info_from_url(self, url, pdf_filename):
        """
        Extrae información básica de la URL cuando no se puede acceder al contenido
        """
        print(f"    ⚠ Extrayendo información de la URL...")
        
        data = {
            'archivo_pdf': pdf_filename,
            'url': url,
            'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'estado': 'URL extraída - Contenido no accesible por SSL'
        }
        
        # Extraer RFC de la URL
        rfc_match = re.search(r'D3=\d+_([A-Z0-9]+)', url)
        if rfc_match:
            data['rfc'] = rfc_match.group(1)
            print(f"    ✓ RFC extraído de URL: {data['rfc']}")
        
        # Extraer parámetros de la URL
        params_match = re.search(r'D1=(\d+)&D2=(\d+)&D3=(.+)', url)
        if params_match:
            data['parametro_d1'] = params_match.group(1)
            data['parametro_d2'] = params_match.group(2)
            data['parametro_d3'] = params_match.group(3)
        
        return data

    def process_all_pdfs(self, directory='.'):
        """
        Procesa todos los PDFs en el directorio
        """
        print("=== Iniciando procesamiento de PDFs del SAT ===")
        
        # Buscar archivos PDF
        pdf_files = [f for f in os.listdir(directory) if f.endswith('.pdf')]
        
        if not pdf_files:
            print("No se encontraron archivos PDF")
            return
        
        print(f"Encontrados {len(pdf_files)} archivos PDF")
        
        for pdf_file in pdf_files:
            print(f"\n--- Procesando {pdf_file} ---")
            
            # Extraer QR
            url = self.extract_qr_from_pdf(pdf_file)
            
            if url:
                # Obtener datos del SAT
                data = self.extract_sat_data(url, pdf_file)
                self.results.append(data)
                
                # Pausa entre peticiones
                time.sleep(2)
            else:
                # Agregar entrada sin URL
                self.results.append({
                    'archivo_pdf': pdf_file,
                    'url': 'No encontrada',
                    'error': 'No se pudo extraer código QR',
                    'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

    def export_to_excel(self, filename='resultados_sat_qr.xlsx'):
        """
        Exporta los resultados a Excel con formato específico para datos del SAT
        """
        if not self.results:
            print("No hay resultados para exportar")
            return
        
        print(f"\n=== Exportando resultados a {filename} ===")
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # Hoja de resumen
                summary_data = []
                for result in self.results:
                    summary_row = {
                        'Archivo PDF': result.get('archivo_pdf', ''),
                        'RFC': result.get('rfc', ''),
                        'URL': result.get('url', ''),
                        'Estado': result.get('estado', result.get('error', 'Procesado')),
                        'Fecha Extracción': result.get('fecha_extraccion', '')
                    }
                    summary_data.append(summary_row)
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Resumen', index=False)
                
                # Hoja de datos detallados del SAT
                detailed_data = []
                for result in self.results:
                    if 'rfc' in result:
                        detailed_row = {
                            'Archivo PDF': result.get('archivo_pdf', ''),
                            'RFC': result.get('rfc', ''),
                            'CURP': result.get('curp', ''),
                            'Nombre': result.get('nombre', ''),
                            'Apellido Paterno': result.get('apellido_paterno', ''),
                            'Apellido Materno': result.get('apellido_materno', ''),
                            'Fecha Nacimiento': result.get('fecha_nacimiento', ''),
                            'Fecha Inicio Operaciones': result.get('fecha_inicio_operaciones', ''),
                            'Situación Contribuyente': result.get('situacion_contribuyente', ''),
                            'Entidad Federativa': result.get('entidad_federativa', ''),
                            'Municipio': result.get('municipio', ''),
                            'Colonia': result.get('colonia', ''),
                            'CP': result.get('cp', ''),
                            'Régimen': result.get('regimen', ''),
                            'URL Original': result.get('url', '')
                        }
                        detailed_data.append(detailed_row)
                
                if detailed_data:
                    df_detailed = pd.DataFrame(detailed_data)
                    df_detailed.to_excel(writer, sheet_name='Datos SAT', index=False)
                
                # Hojas individuales para tablas
                for result in self.results:
                    pdf_name = result.get('archivo_pdf', 'unknown').replace('.pdf', '')
                    sheet_name = re.sub(r'[^\w\s-]', '', pdf_name)[:31]
                    
                    table_data = []
                    for key, value in result.items():
                        if key.startswith('tabla_') and isinstance(value, list):
                            if value:
                                table_data.extend(value)
                                table_data.append([''])  # Separador
                    
                    if table_data:
                        df_sheet = pd.DataFrame(table_data)
                        df_sheet.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
            
            print(f"✓ Resultados exportados exitosamente a {filename}")
            
        except Exception as e:
            print(f"✗ Error exportando a Excel: {str(e)}")

def main():
    """
    Función principal
    """
    processor = SATQRProcessor()
    
    # Procesar todos los PDFs
    processor.process_all_pdfs()
    
    # Exportar resultados
    processor.export_to_excel()
    
    print(f"\n=== Procesamiento completado ===")
    print(f"Total de archivos procesados: {len(processor.results)}")

if __name__ == "__main__":
    main()
