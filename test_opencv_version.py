#!/usr/bin/env python3
"""
Script de prueba para verificar la extracción de QR usando OpenCV
"""

import os
import sys

# Importar la clase principal
try:
    from pdf_qr_extractor_opencv import PDFQRProcessorOpenCV
    print("✓ Importación exitosa de PDFQRProcessorOpenCV")
except ImportError as e:
    print(f"✗ Error importando dependencias: {e}")
    print("Ejecuta primero: python -m pip install -r requirements.txt")
    sys.exit(1)

def test_single_pdf():
    """
    Prueba con un solo PDF para verificar funcionamiento con OpenCV
    """
    print("=== Prueba de Extracción de QR con OpenCV ===")
    
    # Buscar archivos PDF
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No se encontraron archivos PDF en la carpeta actual")
        return False
    
    print(f"📁 Encontrados {len(pdf_files)} archivos PDF:")
    for i, pdf in enumerate(pdf_files, 1):
        print(f"  {i}. {pdf}")
    
    # Seleccionar el primer PDF para prueba
    test_pdf = pdf_files[0]
    print(f"\n🔍 Probando con: {test_pdf}")
    
    # Crear procesador
    processor = PDFQRProcessorOpenCV()
    
    # Intentar extraer QR
    print("\n--- Extrayendo código QR con OpenCV ---")
    url = processor.extract_qr_from_pdf(test_pdf)
    
    if url:
        print(f"✅ QR extraído exitosamente!")
        print(f"🔗 URL encontrada: {url}")
        
        # Intentar acceder a la URL
        print(f"\n--- Probando acceso a URL ---")
        data = processor.extract_data_from_url(url, test_pdf)
        
        if 'error' not in data:
            print(f"✅ Datos extraídos exitosamente!")
            print(f"📄 Título: {data.get('titulo_pagina', 'Sin título')}")
            
            # Mostrar resumen de datos encontrados
            table_count = len([k for k in data.keys() if k.startswith('tabla_')])
            if table_count > 0:
                print(f"📊 Tablas encontradas: {table_count}")
            elif 'contenido_texto' in data:
                print(f"📝 Contenido de texto extraído")
            
            return True
        else:
            print(f"❌ Error accediendo a URL: {data.get('error')}")
            return False
    else:
        print(f"❌ No se pudo extraer código QR del PDF")
        return False

def test_dependencies():
    """
    Verifica que todas las dependencias estén instaladas
    """
    print("\n=== Verificación de Dependencias ===")
    
    dependencies = [
        ('fitz', 'PyMuPDF'),
        ('PIL', 'Pillow'),
        ('cv2', 'opencv-python'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('requests', 'requests'),
        ('bs4', 'beautifulsoup4'),
        ('numpy', 'numpy')
    ]
    
    missing = []
    
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - NO INSTALADO")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Faltan dependencias: {', '.join(missing)}")
        print("💡 Ejecuta: python -m pip install " + " ".join(missing))
        return False
    else:
        print("\n✅ Todas las dependencias están instaladas!")
        return True

def main():
    """
    Función principal de prueba
    """
    print("🔍 Verificando configuración del sistema...\n")
    
    # Verificar dependencias
    deps_ok = test_dependencies()
    
    if not deps_ok:
        print("\n⚠️  Instala las dependencias faltantes antes de continuar.")
        input("Presiona Enter para salir...")
        return
    
    # Probar extracción de QR
    success = test_single_pdf()
    
    if success:
        print(f"\n🎉 ¡Prueba exitosa!")
        print(f"✨ El script principal debería funcionar correctamente.")
        print(f"💡 Ejecuta: python pdf_qr_extractor_opencv.py")
    else:
        print(f"\n⚠️  La prueba falló.")
        print(f"🔧 Revisa los errores mostrados arriba.")
        print(f"📖 Consulta el README.md para solución de problemas.")
    
    input("\nPresiona Enter para continuar...")

if __name__ == "__main__":
    main()
