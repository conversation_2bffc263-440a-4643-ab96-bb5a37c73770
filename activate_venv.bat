@echo off
echo Activando entorno virtual...

if not exist "venv" (
    echo ERROR: El entorno virtual no existe.
    echo Ejecuta primero: setup_venv.bat
    pause
    exit /b 1
)

call venv\Scripts\activate.bat

if %errorlevel% equ 0 (
    echo ✓ Entorno virtual activado.
    echo.
    echo Ahora puedes ejecutar:
    echo - python pdf_qr_extractor.py
    echo - python test_single_pdf.py
    echo.
    echo Para desactivar el entorno virtual, escribe: deactivate
    echo.
) else (
    echo ERROR: No se pudo activar el entorno virtual.
    pause
    exit /b 1
)

cmd /k
