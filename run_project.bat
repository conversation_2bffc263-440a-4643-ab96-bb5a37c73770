@echo off
title Extractor de QR de PDFs - Entorno Virtual
echo.
echo ========================================
echo    EXTRACTOR DE CODIGOS QR DE PDFs
echo         (Usando Entorno Virtual)
echo ========================================
echo.

REM Verificar si existe el entorno virtual
if not exist "venv" (
    echo El entorno virtual no existe.
    echo ¿Quieres crearlo ahora? (S/N)
    set /p create_venv="Respuesta: "
    if /i "%create_venv%"=="S" (
        call setup_venv.bat
        if %errorlevel% neq 0 (
            echo Error configurando el entorno virtual.
            pause
            exit /b 1
        )
    ) else (
        echo Operacion cancelada.
        pause
        exit /b 1
    )
)

REM Activar entorno virtual
echo Activando entorno virtual...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo ERROR: No se pudo activar el entorno virtual.
    echo Intenta ejecutar: setup_venv.bat
    pause
    exit /b 1
)

echo ✓ Entorno virtual activado.
echo.

REM Verificar si existen PDFs
set pdf_count=0
for %%f in (*.pdf) do set /a pdf_count+=1

if %pdf_count% equ 0 (
    echo ADVERTENCIA: No se encontraron archivos PDF en esta carpeta.
    echo.
    echo Coloca los archivos PDF que quieres procesar en esta misma carpeta.
    echo.
    pause
    exit /b 1
)

echo Archivos PDF encontrados: %pdf_count%
echo.

REM Preguntar si hacer prueba primero
echo ¿Quieres hacer una prueba con un solo PDF primero? (recomendado)
echo.
echo 1. Si - Hacer prueba primero
echo 2. No - Procesar todos los PDFs directamente
echo.
set /p choice="Elige una opcion (1 o 2): "

if "%choice%"=="1" (
    echo.
    echo Ejecutando prueba...
    python test_single_pdf.py
    echo.
    echo ¿Continuar con el procesamiento completo? (S/N)
    set /p continue="Respuesta: "
    if /i not "%continue%"=="S" (
        echo Operacion cancelada.
        pause
        exit /b 0
    )
)

echo.
echo Iniciando procesamiento completo...
echo.
python pdf_qr_extractor.py

echo.
echo ========================================
echo           PROCESAMIENTO COMPLETO
echo ========================================
echo.

REM Verificar si se generó el archivo de resultados
if exist "resultados_qr_extraction.xlsx" (
    echo ✓ Archivo de resultados generado: resultados_qr_extraction.xlsx
    echo.
    echo ¿Quieres abrir el archivo de resultados? (S/N)
    set /p open_file="Respuesta: "
    if /i "%open_file%"=="S" (
        start "" "resultados_qr_extraction.xlsx"
    )
) else (
    echo ⚠ No se generó el archivo de resultados.
    echo Revisa los mensajes de error arriba.
)

echo.
echo Para desactivar el entorno virtual, escribe: deactivate
echo.
pause
