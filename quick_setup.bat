@echo off
echo Configuracion rapida del entorno virtual...

REM Buscar Python
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Python no encontrado en PATH.
    echo Buscando en ubicaciones comunes...
    
    if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe" (
        set PYTHON_PATH=%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe
    ) else if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe" (
        set PYTHON_PATH=%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe
    ) else (
        echo No se encontro Python. Instala Python desde python.org
        pause
        exit /b 1
    )
) else (
    set PYTHON_PATH=python
)

echo Usando Python: %PYTHON_PATH%
%PYTHON_PATH% --version

echo.
echo Creando entorno virtual...
%PYTHON_PATH% -m venv venv

echo.
echo Activando entorno virtual...
call venv\Scripts\activate.bat

echo.
echo Instalando dependencias...
python -m pip install --upgrade pip
python -m pip install PyMuPDF pillow opencv-python pyzbar pandas openpyxl requests beautifulsoup4 numpy

echo.
echo Configuracion completada!
echo Para usar el proyecto, ejecuta: run_project.bat

pause
