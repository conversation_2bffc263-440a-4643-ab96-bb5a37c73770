@echo off
echo Instalando dependencias para el extractor de QR de PDFs...
echo.

REM Verificar si Python está instalado
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python no está instalado o no está en el PATH.
    echo Por favor instala Python desde https://www.python.org/downloads/
    echo Asegúrate de marcar "Add Python to PATH" durante la instalación.
    pause
    exit /b 1
)

echo Python encontrado. Instalando dependencias...
echo.

REM Actualizar pip
python -m pip install --upgrade pip

REM Instalar dependencias
python -m pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ✓ Todas las dependencias se instalaron correctamente.
    echo Ya puedes ejecutar el script con: python pdf_qr_extractor.py
) else (
    echo.
    echo ✗ Hubo un error instalando las dependencias.
    echo Revisa los mensajes de error arriba.
)

echo.
pause
