@echo off
echo Instalando dependencias para el extractor de QR de PDFs...
echo.

REM Buscar Python en ubicaciones comunes
set PYTHON_CMD=
for %%i in (python.exe) do set PYTHON_CMD=%%~$PATH:i
if not defined PYTHON_CMD (
    REM Buscar en ubicaciones típicas de instalación
    for %%p in (
        "%USERPROFILE%\AppData\Local\Programs\Python\Python*\python.exe"
        "%PROGRAMFILES%\Python*\python.exe"
        "%PROGRAMFILES(X86)%\Python*\python.exe"
        "%LOCALAPPDATA%\Programs\Python\Python*\python.exe"
    ) do (
        if exist "%%p" set PYTHON_CMD=%%p
    )
)

if not defined PYTHON_CMD (
    echo ERROR: No se pudo encontrar Python instalado.
    echo.
    echo Soluciones:
    echo 1. Instala Python desde https://www.python.org/downloads/
    echo 2. Asegurate de marcar "Add Python to PATH" durante la instalacion
    echo 3. Reinicia la computadora despues de instalar
    echo.
    pause
    exit /b 1
)

echo Python encontrado en: %PYTHON_CMD%
echo.

REM Verificar versión
"%PYTHON_CMD%" --version
echo.

echo Instalando dependencias...
echo.

REM Actualizar pip
"%PYTHON_CMD%" -m pip install --upgrade pip

REM Instalar dependencias
"%PYTHON_CMD%" -m pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ✓ Todas las dependencias se instalaron correctamente.
    echo Ya puedes ejecutar el script.
) else (
    echo.
    echo ✗ Hubo un error instalando las dependencias.
    echo Revisa los mensajes de error arriba.
)

echo.
pause
