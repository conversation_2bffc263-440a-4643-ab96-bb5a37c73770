#!/usr/bin/env python3
"""
Script final para extraer códigos QR de PDFs del SAT con múltiples estrategias
"""

import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
import io
import os
import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from datetime import datetime
import urllib3
import warnings

class FinalSATProcessor:
    def __init__(self):
        self.results = []
        self.qr_detector = cv2.QRCodeDetector()
        
        # Configurar requests para ignorar SSL
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        warnings.filterwarnings('ignore')

    def extract_qr_from_pdf(self, pdf_path):
        """
        Extrae el código QR de la primera página de un PDF
        """
        try:
            print(f"Procesando: {pdf_path}")
            
            doc = fitz.open(pdf_path)
            page = doc[0]
            
            mat = fitz.Matrix(3, 3)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            img = Image.open(io.BytesIO(img_data))
            img_array = np.array(img)
            
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            data, bbox, _ = self.qr_detector.detectAndDecode(gray)
            doc.close()
            
            if data:
                print(f"  ✓ QR encontrado: {data}")
                return data
            else:
                print(f"  ✗ No se encontró código QR")
                return None
                
        except Exception as e:
            print(f"  ✗ Error procesando {pdf_path}: {str(e)}")
            return None

    def extract_sat_info(self, url, pdf_filename):
        """
        Extrae información del SAT con estrategias múltiples
        """
        print(f"  Procesando URL del SAT...")
        
        # Extraer información básica de la URL
        sat_data = {
            'archivo_pdf': pdf_filename,
            'url': url,
            'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Extraer RFC y parámetros de la URL
        rfc_match = re.search(r'D3=(\d+)_([A-Z0-9]+)', url)
        if rfc_match:
            sat_data['numero_registro'] = rfc_match.group(1)
            sat_data['rfc'] = rfc_match.group(2)
            print(f"    ✓ RFC extraído: {sat_data['rfc']}")
            print(f"    ✓ Número de registro: {sat_data['numero_registro']}")
        
        # Extraer parámetros D1 y D2
        params_match = re.search(r'D1=(\d+)&D2=(\d+)', url)
        if params_match:
            sat_data['parametro_d1'] = params_match.group(1)
            sat_data['parametro_d2'] = params_match.group(2)
        
        # Intentar acceder al contenido (con manejo de errores)
        try:
            print(f"    Intentando acceder al contenido...")
            content_data = self.attempt_content_extraction(url)
            if content_data:
                sat_data.update(content_data)
                print(f"    ✓ Contenido extraído exitosamente")
            else:
                print(f"    ⚠ No se pudo acceder al contenido, usando datos de URL")
                sat_data['estado'] = 'Solo datos de URL - Contenido no accesible'
        except Exception as e:
            print(f"    ⚠ Error accediendo al contenido: {str(e)}")
            sat_data['estado'] = 'Solo datos de URL - Error de acceso'
        
        return sat_data

    def attempt_content_extraction(self, url):
        """
        Intenta extraer contenido de la página del SAT
        """
        try:
            # Configurar sesión con múltiples estrategias
            session = requests.Session()
            session.verify = False
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            # Intentar con timeout corto
            response = session.get(url, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                return self.parse_sat_content(soup)
            else:
                return None
                
        except Exception:
            # Si falla, retornar None para usar solo datos de URL
            return None

    def parse_sat_content(self, soup):
        """
        Parsea el contenido HTML del SAT
        """
        data = {}
        
        try:
            text = soup.get_text()
            
            # Patrones mejorados para extraer datos del SAT
            patterns = {
                'curp': r'CURP[:\s]*([A-Z0-9]{18})',
                'nombre': r'Nombre[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Apellido|\s*$)',
                'apellido_paterno': r'Apellido Paterno[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Apellido|\s*$)',
                'apellido_materno': r'Apellido Materno[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Fecha|\s*$)',
                'fecha_nacimiento': r'Fecha Nacimiento[:\s]*(\d{2}-\d{2}-\d{4})',
                'fecha_inicio_operaciones': r'Fecha de Inicio de operaciones[:\s]*(\d{2}-\d{2}-\d{4})',
                'situacion_contribuyente': r'Situación del contribuyente[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Fecha|\s*$)',
                'entidad_federativa': r'Entidad Federativa[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Municipio|\s*$)',
                'municipio': r'Municipio o delegación[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Colonia|\s*$)',
                'colonia': r'Colonia[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\s*Tipo|\s*$)',
                'cp': r'CP[:\s]*(\d{5})',
                'regimen': r'Régimen[:\s]*([^\\n]+?)(?=\s*Fecha|\s*$)',
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    if value and len(value) > 1:
                        data[key] = value
            
            # Buscar tablas
            tables = soup.find_all('table')
            if tables:
                for i, table in enumerate(tables):
                    table_data = self.parse_table(table)
                    if table_data:
                        data[f'tabla_{i+1}'] = table_data
            
        except Exception as e:
            print(f"      ⚠ Error parseando contenido: {str(e)}")
        
        return data

    def parse_table(self, table):
        """
        Parsea una tabla HTML
        """
        try:
            rows = []
            for row in table.find_all('tr'):
                cells = []
                for cell in row.find_all(['td', 'th']):
                    text = cell.get_text(strip=True)
                    if text:
                        cells.append(text)
                
                if cells:
                    rows.append(cells)
            
            return rows if rows else None
            
        except Exception:
            return None

    def process_all_pdfs(self, directory='.'):
        """
        Procesa todos los PDFs en el directorio
        """
        print("=== EXTRACTOR FINAL DE QR DEL SAT ===")
        print("Procesando PDFs y extrayendo información del SAT...")
        
        pdf_files = [f for f in os.listdir(directory) if f.endswith('.pdf')]
        
        if not pdf_files:
            print("No se encontraron archivos PDF")
            return
        
        print(f"Encontrados {len(pdf_files)} archivos PDF\n")
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"[{i}/{len(pdf_files)}] Procesando {pdf_file}")
            
            url = self.extract_qr_from_pdf(pdf_file)
            
            if url:
                data = self.extract_sat_info(url, pdf_file)
                self.results.append(data)
            else:
                self.results.append({
                    'archivo_pdf': pdf_file,
                    'url': 'No encontrada',
                    'error': 'No se pudo extraer código QR',
                    'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            print()  # Línea en blanco entre archivos

    def export_to_excel(self, filename='resultados_sat_final.xlsx'):
        """
        Exporta los resultados a Excel con formato optimizado
        """
        if not self.results:
            print("No hay resultados para exportar")
            return
        
        print(f"=== Exportando resultados a {filename} ===")
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # Hoja 1: Resumen ejecutivo
                summary_data = []
                for result in self.results:
                    nombre_completo = ""
                    if result.get('nombre') and result.get('apellido_paterno'):
                        nombre_completo = f"{result.get('nombre', '')} {result.get('apellido_paterno', '')} {result.get('apellido_materno', '')}".strip()
                    
                    summary_row = {
                        'Archivo PDF': result.get('archivo_pdf', ''),
                        'RFC': result.get('rfc', ''),
                        'Nombre Completo': nombre_completo,
                        'CURP': result.get('curp', ''),
                        'Situación': result.get('situacion_contribuyente', ''),
                        'Municipio': result.get('municipio', ''),
                        'Estado': result.get('entidad_federativa', ''),
                        'CP': result.get('cp', ''),
                        'Régimen': result.get('regimen', ''),
                        'URL SAT': result.get('url', ''),
                        'Estado Procesamiento': result.get('estado', result.get('error', 'Procesado'))
                    }
                    summary_data.append(summary_row)
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Resumen Ejecutivo', index=False)
                
                # Hoja 2: Datos completos
                detailed_data = []
                for result in self.results:
                    if 'rfc' in result:
                        detailed_row = {
                            'Archivo PDF': result.get('archivo_pdf', ''),
                            'RFC': result.get('rfc', ''),
                            'Número Registro': result.get('numero_registro', ''),
                            'CURP': result.get('curp', ''),
                            'Nombre': result.get('nombre', ''),
                            'Apellido Paterno': result.get('apellido_paterno', ''),
                            'Apellido Materno': result.get('apellido_materno', ''),
                            'Fecha Nacimiento': result.get('fecha_nacimiento', ''),
                            'Fecha Inicio Operaciones': result.get('fecha_inicio_operaciones', ''),
                            'Situación Contribuyente': result.get('situacion_contribuyente', ''),
                            'Entidad Federativa': result.get('entidad_federativa', ''),
                            'Municipio': result.get('municipio', ''),
                            'Colonia': result.get('colonia', ''),
                            'CP': result.get('cp', ''),
                            'Régimen': result.get('regimen', ''),
                            'URL Original': result.get('url', ''),
                            'Fecha Extracción': result.get('fecha_extraccion', '')
                        }
                        detailed_data.append(detailed_row)
                
                if detailed_data:
                    df_detailed = pd.DataFrame(detailed_data)
                    df_detailed.to_excel(writer, sheet_name='Datos Completos', index=False)
                
                # Hoja 3: URLs extraídas
                url_data = []
                for result in self.results:
                    url_row = {
                        'Archivo PDF': result.get('archivo_pdf', ''),
                        'RFC': result.get('rfc', ''),
                        'URL Completa': result.get('url', ''),
                        'Parámetro D1': result.get('parametro_d1', ''),
                        'Parámetro D2': result.get('parametro_d2', ''),
                        'Número Registro': result.get('numero_registro', ''),
                        'Estado': result.get('estado', result.get('error', 'OK'))
                    }
                    url_data.append(url_row)
                
                df_urls = pd.DataFrame(url_data)
                df_urls.to_excel(writer, sheet_name='URLs Extraídas', index=False)
            
            print(f"✓ Archivo Excel generado exitosamente: {filename}")
            print(f"✓ Contiene {len(self.results)} registros procesados")
            
            # Mostrar estadísticas
            successful_extractions = len([r for r in self.results if 'rfc' in r])
            print(f"✓ RFCs extraídos exitosamente: {successful_extractions}/{len(self.results)}")
            
        except Exception as e:
            print(f"✗ Error exportando a Excel: {str(e)}")

def main():
    """
    Función principal
    """
    print("🚀 EXTRACTOR FINAL DE CÓDIGOS QR DEL SAT")
    print("=" * 50)
    
    processor = FinalSATProcessor()
    
    # Procesar todos los PDFs
    processor.process_all_pdfs()
    
    # Exportar resultados
    processor.export_to_excel()
    
    print("\n" + "=" * 50)
    print("🎉 PROCESAMIENTO COMPLETADO")
    print(f"📊 Total de archivos procesados: {len(processor.results)}")
    print("📁 Revisa el archivo Excel generado para ver los resultados")

if __name__ == "__main__":
    main()
