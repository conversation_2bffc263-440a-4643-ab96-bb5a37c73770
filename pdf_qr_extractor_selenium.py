#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> para extraer códigos QR de PDFs del SAT usando Selenium para acceder a las páginas web
"""

import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
import io
import os
import pandas as pd
import time
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

class SATSeleniumProcessor:
    def __init__(self):
        self.results = []
        self.driver = None
        self.qr_detector = cv2.QRCodeDetector()
        self.setup_driver()

    def setup_driver(self):
        """
        Configura el driver de Selenium con Chrome
        """
        try:
            print("Configurando navegador Chrome...")
            
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Ejecutar sin ventana
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Configuraciones SSL más permisivas
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--ignore-ssl-errors")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--ignore-certificate-errors-spki-list")
            
            # Instalar ChromeDriver automáticamente
            service = Service(ChromeDriverManager().install())
            
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(30)
            
            print("✓ Navegador Chrome configurado correctamente")
            
        except Exception as e:
            print(f"✗ Error configurando Chrome: {str(e)}")
            print("Intentando con modo visible...")
            try:
                # Intentar sin headless
                chrome_options = Options()
                chrome_options.add_argument("--ignore-certificate-errors")
                chrome_options.add_argument("--ignore-ssl-errors")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                print("✓ Chrome configurado en modo visible")
            except Exception as e2:
                print(f"✗ Error crítico configurando Chrome: {str(e2)}")
                self.driver = None

    def extract_qr_from_pdf(self, pdf_path):
        """
        Extrae el código QR de la primera página de un PDF
        """
        try:
            print(f"Procesando: {pdf_path}")
            
            doc = fitz.open(pdf_path)
            page = doc[0]
            
            mat = fitz.Matrix(3, 3)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            img = Image.open(io.BytesIO(img_data))
            img_array = np.array(img)
            
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            data, bbox, _ = self.qr_detector.detectAndDecode(gray)
            doc.close()
            
            if data:
                print(f"  ✓ QR encontrado: {data}")
                return data
            else:
                print(f"  ✗ No se encontró código QR")
                return None
                
        except Exception as e:
            print(f"  ✗ Error procesando {pdf_path}: {str(e)}")
            return None

    def extract_sat_data_selenium(self, url, pdf_filename):
        """
        Extrae datos del SAT usando Selenium
        """
        if not self.driver:
            return self.extract_info_from_url(url, pdf_filename)
        
        try:
            print(f"  Accediendo a URL con Selenium: {url}")
            
            # Navegar a la URL
            self.driver.get(url)
            
            # Esperar a que la página cargue
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Obtener el HTML de la página
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Extraer datos
            sat_data = {
                'archivo_pdf': pdf_filename,
                'url': url,
                'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Extraer RFC de la URL
            rfc_match = re.search(r'D3=\d+_([A-Z0-9]+)', url)
            if rfc_match:
                sat_data['rfc'] = rfc_match.group(1)
            
            # Extraer datos específicos del SAT
            sat_data.update(self.extract_sat_specific_data(soup))
            
            # Buscar tablas
            tables = soup.find_all('table')
            if tables:
                print(f"    ✓ Encontradas {len(tables)} tabla(s)")
                for i, table in enumerate(tables):
                    table_data = self.parse_sat_table(table)
                    if table_data:
                        sat_data[f'tabla_{i+1}'] = table_data
            
            print(f"    ✓ Datos extraídos exitosamente")
            return sat_data
            
        except Exception as e:
            print(f"    ✗ Error con Selenium: {str(e)}")
            return self.extract_info_from_url(url, pdf_filename)

    def extract_sat_specific_data(self, soup):
        """
        Extrae datos específicos del formato del SAT
        """
        data = {}
        
        try:
            text = soup.get_text()
            
            # Patrones para extraer datos del SAT
            patterns = {
                'curp': r'CURP[:\s]*([A-Z0-9]{18})',
                'nombre': r'Nombre[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Apellido|$)',
                'apellido_paterno': r'Apellido Paterno[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Apellido|$)',
                'apellido_materno': r'Apellido Materno[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Fecha|$)',
                'fecha_nacimiento': r'Fecha Nacimiento[:\s]*(\d{2}-\d{2}-\d{4})',
                'fecha_inicio_operaciones': r'Fecha de Inicio de operaciones[:\s]*(\d{2}-\d{2}-\d{4})',
                'situacion_contribuyente': r'Situación del contribuyente[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Fecha|$)',
                'entidad_federativa': r'Entidad Federativa[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Municipio|$)',
                'municipio': r'Municipio o delegación[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Colonia|$)',
                'colonia': r'Colonia[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Tipo|$)',
                'tipo_vialidad': r'Tipo de vialidad[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Nombre|$)',
                'nombre_vialidad': r'Nombre de la vialidad[:\s]*([A-ZÁÉÍÓÚÑ\s]+?)(?=\n|Número|$)',
                'numero_exterior': r'Número exterior[:\s]*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?=\n|Número|CP|$)',
                'cp': r'CP[:\s]*(\d{5})',
                'correo': r'Correo electrónico[:\s]*([A-Za-z0-9@._-]+)',
                'regimen': r'Régimen[:\s]*([A-ZÁÉÍÓÚÑ\s,]+?)(?=\n|Fecha|$)',
                'fecha_alta': r'Fecha de alta[:\s]*(\d{2}-\d{2}-\d{4})',
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    if value and value != ':':
                        data[key] = value
                        print(f"    ✓ {key}: {value}")
            
        except Exception as e:
            print(f"      ⚠ Error extrayendo datos específicos: {str(e)}")
        
        return data

    def parse_sat_table(self, table):
        """
        Parsea tablas del SAT
        """
        try:
            rows = []
            for row in table.find_all('tr'):
                cells = []
                for cell in row.find_all(['td', 'th']):
                    text = cell.get_text(strip=True)
                    if text:
                        cells.append(text)
                
                if cells:
                    rows.append(cells)
            
            return rows if rows else None
            
        except Exception as e:
            print(f"      ✗ Error parseando tabla: {str(e)}")
            return None

    def extract_info_from_url(self, url, pdf_filename):
        """
        Extrae información básica de la URL cuando no se puede acceder al contenido
        """
        print(f"    ⚠ Extrayendo información de la URL...")
        
        data = {
            'archivo_pdf': pdf_filename,
            'url': url,
            'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'estado': 'URL extraída - Contenido no accesible'
        }
        
        # Extraer RFC de la URL
        rfc_match = re.search(r'D3=\d+_([A-Z0-9]+)', url)
        if rfc_match:
            data['rfc'] = rfc_match.group(1)
            print(f"    ✓ RFC extraído de URL: {data['rfc']}")
        
        return data

    def process_all_pdfs(self, directory='.'):
        """
        Procesa todos los PDFs en el directorio
        """
        print("=== Iniciando procesamiento de PDFs del SAT con Selenium ===")
        
        pdf_files = [f for f in os.listdir(directory) if f.endswith('.pdf')]
        
        if not pdf_files:
            print("No se encontraron archivos PDF")
            return
        
        print(f"Encontrados {len(pdf_files)} archivos PDF")
        
        for pdf_file in pdf_files:
            print(f"\n--- Procesando {pdf_file} ---")
            
            url = self.extract_qr_from_pdf(pdf_file)
            
            if url:
                data = self.extract_sat_data_selenium(url, pdf_file)
                self.results.append(data)
                time.sleep(3)  # Pausa más larga para Selenium
            else:
                self.results.append({
                    'archivo_pdf': pdf_file,
                    'url': 'No encontrada',
                    'error': 'No se pudo extraer código QR',
                    'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

    def export_to_excel(self, filename='resultados_sat_selenium.xlsx'):
        """
        Exporta los resultados a Excel
        """
        if not self.results:
            print("No hay resultados para exportar")
            return
        
        print(f"\n=== Exportando resultados a {filename} ===")
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # Hoja de resumen
                summary_data = []
                for result in self.results:
                    summary_row = {
                        'Archivo PDF': result.get('archivo_pdf', ''),
                        'RFC': result.get('rfc', ''),
                        'Nombre Completo': f"{result.get('nombre', '')} {result.get('apellido_paterno', '')} {result.get('apellido_materno', '')}".strip(),
                        'CURP': result.get('curp', ''),
                        'Situación': result.get('situacion_contribuyente', ''),
                        'Municipio': result.get('municipio', ''),
                        'Estado': result.get('entidad_federativa', ''),
                        'URL': result.get('url', ''),
                        'Fecha Extracción': result.get('fecha_extraccion', '')
                    }
                    summary_data.append(summary_row)
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Resumen', index=False)
                
                # Hoja de datos detallados
                detailed_data = []
                for result in self.results:
                    if 'rfc' in result:
                        detailed_row = {
                            'Archivo PDF': result.get('archivo_pdf', ''),
                            'RFC': result.get('rfc', ''),
                            'CURP': result.get('curp', ''),
                            'Nombre': result.get('nombre', ''),
                            'Apellido Paterno': result.get('apellido_paterno', ''),
                            'Apellido Materno': result.get('apellido_materno', ''),
                            'Fecha Nacimiento': result.get('fecha_nacimiento', ''),
                            'Fecha Inicio Operaciones': result.get('fecha_inicio_operaciones', ''),
                            'Situación Contribuyente': result.get('situacion_contribuyente', ''),
                            'Entidad Federativa': result.get('entidad_federativa', ''),
                            'Municipio': result.get('municipio', ''),
                            'Colonia': result.get('colonia', ''),
                            'Tipo Vialidad': result.get('tipo_vialidad', ''),
                            'Nombre Vialidad': result.get('nombre_vialidad', ''),
                            'Número Exterior': result.get('numero_exterior', ''),
                            'CP': result.get('cp', ''),
                            'Correo': result.get('correo', ''),
                            'Régimen': result.get('regimen', ''),
                            'Fecha Alta': result.get('fecha_alta', ''),
                            'URL Original': result.get('url', '')
                        }
                        detailed_data.append(detailed_row)
                
                if detailed_data:
                    df_detailed = pd.DataFrame(detailed_data)
                    df_detailed.to_excel(writer, sheet_name='Datos Completos SAT', index=False)
            
            print(f"✓ Resultados exportados exitosamente a {filename}")
            
        except Exception as e:
            print(f"✗ Error exportando a Excel: {str(e)}")

    def close(self):
        """
        Cierra el navegador
        """
        if self.driver:
            self.driver.quit()
            print("✓ Navegador cerrado")

def main():
    """
    Función principal
    """
    processor = SATSeleniumProcessor()
    
    try:
        # Procesar todos los PDFs
        processor.process_all_pdfs()
        
        # Exportar resultados
        processor.export_to_excel()
        
        print(f"\n=== Procesamiento completado ===")
        print(f"Total de archivos procesados: {len(processor.results)}")
        
    finally:
        # Cerrar navegador
        processor.close()

if __name__ == "__main__":
    main()
