# 🚀 Instrucciones de Configuración Manual

## Paso 1: Verificar Python

Abre **Command Prompt** (cmd) o **PowerShell** y ejecuta:

```cmd
python --version
```

Si ves algo como "Python 3.13.5", ¡perfecto! Si no, prueba:

```cmd
py --version
```

## Paso 2: Crear Entorno Virtual

En la carpeta del proyecto, ejecuta:

```cmd
python -m venv venv
```

O si el comando anterior no funciona:

```cmd
py -m venv venv
```

## Paso 3: Activar Entorno Virtual

```cmd
venv\Scripts\activate
```

Deberías ver `(venv)` al inicio de tu línea de comandos.

## Paso 4: Instalar Dependencias

```cmd
python -m pip install --upgrade pip
python -m pip install PyMuPDF pillow opencv-python pyzbar pandas openpyxl requests beautifulsoup4 numpy
```

## Paso 5: Verificar Instalación

```cmd
python check_python.py
```

## Paso 6: Ejecutar el Proyecto

### Opción A: Script de prueba
```cmd
python test_single_pdf.py
```

### Opción B: Procesamiento completo
```cmd
python pdf_qr_extractor.py
```

## 🔧 Solución de Problemas

### Error: "python no se reconoce"
- Reinstala Python desde https://www.python.org/downloads/
- Marca "Add Python to PATH" durante la instalación
- Reinicia la computadora

### Error: "Microsoft Visual C++ 14.0 is required"
- Instala Visual Studio Build Tools
- O instala Visual Studio Community

### Error: "No module named 'cv2'"
- Ejecuta: `pip install opencv-python`
- Si falla, prueba: `pip install opencv-python-headless`

## 📁 Estructura Final

Después de la configuración, deberías tener:

```
📁 Tu carpeta/
├── 📁 venv/                    (entorno virtual)
├── 📄 pdf_qr_extractor.py     (script principal)
├── 📄 test_single_pdf.py      (script de prueba)
├── 📄 requirements.txt        (dependencias)
├── 📄 *.pdf                   (tus archivos PDF)
└── 📄 resultados_qr_extraction.xlsx (resultados)
```

## 🎯 Comandos Rápidos

### Para usar el proyecto diariamente:

1. Abrir cmd en la carpeta del proyecto
2. Activar entorno: `venv\Scripts\activate`
3. Ejecutar: `python pdf_qr_extractor.py`
4. Desactivar: `deactivate`

### Scripts automáticos disponibles:

- `quick_setup.bat` - Configuración automática
- `run_project.bat` - Ejecutar proyecto completo
- `activate_venv.bat` - Solo activar entorno virtual
