#!/usr/bin/env python3
"""
Script de prueba para generar archivo con nombre único
"""

from pdf_qr_scraper_advanced import AdvancedSATScraper
from datetime import datetime

def main():
    print("🌐 GENERANDO ARCHIVO CON DATOS DE PDF + SCRAPING")
    print("=" * 60)
    
    scraper = AdvancedSATScraper()
    
    # Procesar todos los PDFs
    scraper.process_all_pdfs()
    
    # Generar archivo con timestamp único
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'resultados_completos_{timestamp}.xlsx'
    
    # Exportar resultados
    scraper.export_to_excel(filename)
    
    print(f"\n🎉 ARCHIVO GENERADO: {filename}")
    print("📁 Contiene 4 hojas:")
    print("   1. Resumen Scraping")
    print("   2. Datos Extraídos (web)")
    print("   3. Datos del PDF")
    print("   4. Estadísticas")

if __name__ == "__main__":
    main()
