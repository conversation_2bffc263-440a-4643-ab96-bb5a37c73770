@echo off
echo Buscando Python...

REM Buscar Python en ubicaciones comunes
set PYTHON_CMD=

REM Probar ubicaciones típicas una por una
if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe" (
    set PYTHON_CMD=%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe
    goto :found
)

if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe" (
    set PYTHON_CMD=%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe
    goto :found
)

if exist "%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe" (
    set PYTHON_CMD=%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe
    goto :found
)

if exist "%PROGRAMFILES%\Python313\python.exe" (
    set PYTHON_CMD=%PROGRAMFILES%\Python313\python.exe
    goto :found
)

if exist "%PROGRAMFILES%\Python312\python.exe" (
    set PYTHON_CMD=%PROGRAMFILES%\Python312\python.exe
    goto :found
)

if exist "%PROGRAMFILES%\Python311\python.exe" (
    set PYTHON_CMD=%PROGRAMFILES%\Python311\python.exe
    goto :found
)

REM Buscar dinámicamente en directorios Python
for /d %%p in ("%USERPROFILE%\AppData\Local\Programs\Python\Python*") do (
    if exist "%%p\python.exe" (
        set PYTHON_CMD=%%p\python.exe
        goto :found
    )
)

:found

if not defined PYTHON_CMD (
    echo ERROR: No se pudo encontrar Python.
    echo Asegurate de que Python este instalado.
    pause
    exit /b 1
)

echo Python encontrado: %PYTHON_CMD%
echo.

echo Ejecutando script de prueba...
"%PYTHON_CMD%" test_python.py

pause
