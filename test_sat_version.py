#!/usr/bin/env python3
"""
Script de prueba para la versión especializada del SAT
"""

import os
import sys

try:
    from pdf_qr_extractor_sat import SATQRProcessor
    print("✓ Importación exitosa de SATQRProcessor")
except ImportError as e:
    print(f"✗ Error importando: {e}")
    sys.exit(1)

def test_sat_extraction():
    """
    Prueba específica para extracción de datos del SAT
    """
    print("=== Prueba de Extracción SAT Especializada ===")
    
    # Buscar archivos PDF
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No se encontraron archivos PDF")
        return False
    
    print(f"📁 Encontrados {len(pdf_files)} archivos PDF")
    
    # Seleccionar el primer PDF para prueba
    test_pdf = pdf_files[0]
    print(f"\n🔍 Probando con: {test_pdf}")
    
    # Crear procesador SAT
    processor = SATQRProcessor()
    
    # Extraer QR
    print("\n--- Extrayendo código QR ---")
    url = processor.extract_qr_from_pdf(test_pdf)
    
    if url:
        print(f"✅ QR extraído: {url}")
        
        # Extraer datos del SAT
        print(f"\n--- Extrayendo datos del SAT ---")
        data = processor.extract_sat_data(url, test_pdf)
        
        print(f"\n📊 Datos extraídos:")
        for key, value in data.items():
            if key not in ['archivo_pdf', 'url', 'fecha_extraccion']:
                print(f"  {key}: {value}")
        
        return True
    else:
        print(f"❌ No se pudo extraer QR")
        return False

def main():
    """
    Función principal de prueba
    """
    print("🔍 Probando extractor SAT especializado...\n")
    
    success = test_sat_extraction()
    
    if success:
        print(f"\n🎉 ¡Prueba exitosa!")
        print(f"💡 Ejecuta: python pdf_qr_extractor_sat.py")
    else:
        print(f"\n⚠️  La prueba falló.")
    
    input("\nPresiona Enter para continuar...")

if __name__ == "__main__":
    main()
