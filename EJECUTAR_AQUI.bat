@echo off
title Extractor QR - Configuracion Automatica
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    EXTRACTOR DE QR DE PDFs                   ║
echo  ║                     Configuracion Automatica                 ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/5] Verificando Python...
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python encontrado con 'py'
    set PYTHON_CMD=py
    goto :python_found
)

python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python encontrado con 'python'
    set PYTHON_CMD=python
    goto :python_found
)

echo ✗ Python no encontrado
echo.
echo Por favor:
echo 1. Instala Python desde https://www.python.org/downloads/
echo 2. Marca "Add Python to PATH" durante la instalacion
echo 3. Reinicia la computadora
echo 4. Ejecuta este script nuevamente
echo.
pause
exit /b 1

:python_found
%PYTHON_CMD% --version
echo.

echo [2/5] Creando entorno virtual...
if exist venv (
    echo ✓ Entorno virtual ya existe
) else (
    %PYTHON_CMD% -m venv venv
    if %errorlevel% equ 0 (
        echo ✓ Entorno virtual creado
    ) else (
        echo ✗ Error creando entorno virtual
        pause
        exit /b 1
    )
)
echo.

echo [3/5] Activando entorno virtual...
call venv\Scripts\activate.bat
if %errorlevel% equ 0 (
    echo ✓ Entorno virtual activado
) else (
    echo ✗ Error activando entorno virtual
    pause
    exit /b 1
)
echo.

echo [4/5] Instalando dependencias...
python -m pip install --upgrade pip --quiet
python -m pip install PyMuPDF pillow opencv-python pyzbar pandas openpyxl requests beautifulsoup4 numpy --quiet
if %errorlevel% equ 0 (
    echo ✓ Dependencias instaladas
) else (
    echo ⚠ Algunas dependencias pueden haber fallado
    echo   El script intentara continuar...
)
echo.

echo [5/5] Verificando archivos PDF...
set pdf_count=0
for %%f in (*.pdf) do set /a pdf_count+=1
echo ✓ Archivos PDF encontrados: %pdf_count%
echo.

if %pdf_count% equ 0 (
    echo ⚠ ADVERTENCIA: No hay archivos PDF en esta carpeta
    echo   Coloca tus archivos PDF aqui y ejecuta el script nuevamente
    echo.
)

echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                      CONFIGURACION COMPLETA                  ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

if %pdf_count% gtr 0 (
    echo ¿Quieres ejecutar el extractor ahora? (S/N)
    set /p run_now="Respuesta: "
    if /i "%run_now%"=="S" (
        echo.
        echo Ejecutando extractor de QR...
        python pdf_qr_extractor.py
        
        if exist "resultados_qr_extraction.xlsx" (
            echo.
            echo ✓ Procesamiento completado!
            echo ✓ Archivo generado: resultados_qr_extraction.xlsx
            echo.
            echo ¿Abrir archivo de resultados? (S/N)
            set /p open_file="Respuesta: "
            if /i "%open_file%"=="S" (
                start "" "resultados_qr_extraction.xlsx"
            )
        )
    )
)

echo.
echo Para usar el proyecto en el futuro:
echo 1. Ejecuta: activate_venv.bat
echo 2. Luego: python pdf_qr_extractor.py
echo.
pause
