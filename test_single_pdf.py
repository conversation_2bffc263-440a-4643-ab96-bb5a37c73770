#!/usr/bin/env python3
"""
Script de prueba para verificar la extracción de QR de un solo PDF
"""

import os
import sys

# Importar la clase principal
try:
    from pdf_qr_extractor import PDFQRProcessor
except ImportError as e:
    print(f"Error importando dependencias: {e}")
    print("Ejecuta primero: python -m pip install -r requirements.txt")
    sys.exit(1)

def test_single_pdf():
    """
    Prueba con un solo PDF para verificar funcionamiento
    """
    print("=== Prueba de Extracción de QR ===")
    
    # Buscar archivos PDF
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No se encontraron archivos PDF en la carpeta actual")
        return False
    
    print(f"📁 Encontrados {len(pdf_files)} archivos PDF:")
    for i, pdf in enumerate(pdf_files, 1):
        print(f"  {i}. {pdf}")
    
    # Seleccionar el primer PDF para prueba
    test_pdf = pdf_files[0]
    print(f"\n🔍 Probando con: {test_pdf}")
    
    # Crear procesador
    processor = PDFQRProcessor()
    
    # Intentar extraer QR
    print("\n--- Extrayendo código QR ---")
    url = processor.extract_qr_from_pdf(test_pdf)
    
    if url:
        print(f"✅ QR extraído exitosamente!")
        print(f"🔗 URL encontrada: {url}")
        
        # Intentar acceder a la URL
        print(f"\n--- Probando acceso a URL ---")
        data = processor.extract_data_from_url(url, test_pdf)
        
        if 'error' not in data:
            print(f"✅ Datos extraídos exitosamente!")
            print(f"📄 Título: {data.get('titulo_pagina', 'Sin título')}")
            
            # Mostrar resumen de datos encontrados
            table_count = len([k for k in data.keys() if k.startswith('tabla_')])
            if table_count > 0:
                print(f"📊 Tablas encontradas: {table_count}")
            elif 'contenido_texto' in data:
                print(f"📝 Contenido de texto extraído")
            
            return True
        else:
            print(f"❌ Error accediendo a URL: {data.get('error')}")
            return False
    else:
        print(f"❌ No se pudo extraer código QR del PDF")
        return False

def main():
    """
    Función principal de prueba
    """
    success = test_single_pdf()
    
    if success:
        print(f"\n🎉 ¡Prueba exitosa!")
        print(f"✨ El script principal debería funcionar correctamente.")
        print(f"💡 Ejecuta: python pdf_qr_extractor.py")
    else:
        print(f"\n⚠️  La prueba falló.")
        print(f"🔧 Revisa los errores mostrados arriba.")
        print(f"📖 Consulta el README.md para solución de problemas.")

if __name__ == "__main__":
    main()
