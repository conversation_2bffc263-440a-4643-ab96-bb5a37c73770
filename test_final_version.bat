@echo off
echo Probando version final del extractor SAT...

call venv\Scripts\activate.bat
python pdf_qr_extractor_final.py

echo.
echo Verificando resultados...
if exist "resultados_sat_final.xlsx" (
    echo ✓ Archivo generado exitosamente!
    echo ¿Abrir archivo de resultados? (S/N)
    set /p open_file="Respuesta: "
    if /i "%open_file%"=="S" (
        start "" "resultados_sat_final.xlsx"
    )
) else (
    echo ✗ No se generó el archivo de resultados
)

pause
