#!/usr/bin/env python3
"""
Script completo para extraer códigos QR de PDFs usando OpenCV (compatible con Windows)
"""

import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
import io
import os
import requests
from bs4 import BeautifulSoup
import pandas as pd
from urllib.parse import urljoin, urlparse
import time
import re
from datetime import datetime
import ssl
import urllib3

class PDFQRProcessorOpenCV:
    def __init__(self):
        self.results = []
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Configurar SSL para manejar servidores con configuraciones obsoletas
        self.session.verify = False  # Deshabilitar verificación SSL estricta
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # Configurar adaptador HTTP con configuraciones SSL más flexibles
        adapter = requests.adapters.HTTPAdapter()
        self.session.mount('https://', adapter)
        self.session.mount('http://', adapter)

        # Inicializar detector QR de OpenCV
        self.qr_detector = cv2.QRCodeDetector()

    def extract_qr_from_pdf(self, pdf_path):
        """
        Extrae el código QR de la primera página de un PDF usando OpenCV
        """
        try:
            print(f"Procesando: {pdf_path}")
            
            # Abrir el PDF
            doc = fitz.open(pdf_path)
            
            # Obtener la primera página
            page = doc[0]
            
            # Convertir la página a imagen con alta resolución
            mat = fitz.Matrix(3, 3)  # Escalar para mejor resolución
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # Convertir a imagen PIL
            img = Image.open(io.BytesIO(img_data))
            
            # Convertir a array numpy para OpenCV
            img_array = np.array(img)
            
            # Convertir a escala de grises si es necesario
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # Detectar códigos QR usando OpenCV
            data, bbox, _ = self.qr_detector.detectAndDecode(gray)
            
            doc.close()
            
            if data:
                print(f"  ✓ QR encontrado: {data}")
                return data
            else:
                print(f"  ✗ No se encontró código QR")
                return None
                
        except Exception as e:
            print(f"  ✗ Error procesando {pdf_path}: {str(e)}")
            return None

    def extract_data_from_url(self, url, pdf_filename):
        """
        Extrae información tabular de una URL
        """
        try:
            print(f"  Accediendo a URL: {url}")
            
            # Realizar petición HTTP
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Parsear HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Buscar tablas
            tables = soup.find_all('table')
            
            data = {
                'archivo_pdf': pdf_filename,
                'url': url,
                'titulo_pagina': soup.title.string.strip() if soup.title else 'Sin título',
                'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if tables:
                print(f"    ✓ Encontradas {len(tables)} tabla(s)")
                
                # Procesar cada tabla
                for i, table in enumerate(tables):
                    table_data = self.parse_table(table)
                    if table_data:
                        data[f'tabla_{i+1}'] = table_data
            else:
                # Si no hay tablas, extraer texto estructurado
                print(f"    ⚠ No se encontraron tablas, extrayendo texto")
                text_data = self.extract_structured_text(soup)
                data['contenido_texto'] = text_data
            
            return data
            
        except requests.RequestException as e:
            print(f"    ✗ Error de conexión: {str(e)}")
            return {
                'archivo_pdf': pdf_filename,
                'url': url,
                'error': f'Error de conexión: {str(e)}',
                'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            print(f"    ✗ Error procesando URL: {str(e)}")
            return {
                'archivo_pdf': pdf_filename,
                'url': url,
                'error': f'Error: {str(e)}',
                'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def parse_table(self, table):
        """
        Convierte una tabla HTML en datos estructurados
        """
        try:
            rows = []
            
            # Buscar filas
            for row in table.find_all('tr'):
                cells = []
                for cell in row.find_all(['td', 'th']):
                    # Limpiar texto de la celda
                    text = cell.get_text(strip=True)
                    cells.append(text)
                
                if cells:  # Solo agregar filas no vacías
                    rows.append(cells)
            
            return rows
            
        except Exception as e:
            print(f"      ✗ Error parseando tabla: {str(e)}")
            return None

    def extract_structured_text(self, soup):
        """
        Extrae texto estructurado cuando no hay tablas
        """
        try:
            # Remover scripts y estilos
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Obtener texto
            text = soup.get_text()
            
            # Limpiar y estructurar
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            
            # Buscar patrones comunes de datos estructurados
            structured_data = {}
            
            for line in lines:
                # Buscar patrones clave:valor
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        if key and value:
                            structured_data[key] = value
            
            return structured_data if structured_data else '\n'.join(lines[:50])  # Primeras 50 líneas
            
        except Exception as e:
            print(f"      ✗ Error extrayendo texto: {str(e)}")
            return "Error extrayendo contenido"

    def process_all_pdfs(self, directory='.'):
        """
        Procesa todos los PDFs en el directorio
        """
        print("=== Iniciando procesamiento de PDFs ===")
        
        # Buscar archivos PDF
        pdf_files = [f for f in os.listdir(directory) if f.endswith('.pdf')]
        
        if not pdf_files:
            print("No se encontraron archivos PDF")
            return
        
        print(f"Encontrados {len(pdf_files)} archivos PDF")
        
        for pdf_file in pdf_files:
            print(f"\n--- Procesando {pdf_file} ---")
            
            # Extraer QR
            url = self.extract_qr_from_pdf(pdf_file)
            
            if url:
                # Obtener datos de la URL
                data = self.extract_data_from_url(url, pdf_file)
                self.results.append(data)
                
                # Pausa entre peticiones para ser respetuoso
                time.sleep(1)
            else:
                # Agregar entrada sin URL
                self.results.append({
                    'archivo_pdf': pdf_file,
                    'url': 'No encontrada',
                    'error': 'No se pudo extraer código QR',
                    'fecha_extraccion': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

    def export_to_excel(self, filename='resultados_qr_extraction.xlsx'):
        """
        Exporta los resultados a Excel
        """
        if not self.results:
            print("No hay resultados para exportar")
            return
        
        print(f"\n=== Exportando resultados a {filename} ===")
        
        try:
            # Crear un escritor de Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # Hoja principal con resumen
                summary_data = []
                for result in self.results:
                    summary_row = {
                        'Archivo PDF': result.get('archivo_pdf', ''),
                        'URL': result.get('url', ''),
                        'Título': result.get('titulo_pagina', ''),
                        'Fecha Extracción': result.get('fecha_extraccion', ''),
                        'Error': result.get('error', '')
                    }
                    summary_data.append(summary_row)
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Resumen', index=False)
                
                # Hojas individuales para cada PDF con tablas
                for result in self.results:
                    pdf_name = result.get('archivo_pdf', 'unknown').replace('.pdf', '')
                    
                    # Limpiar nombre para usar como nombre de hoja
                    sheet_name = re.sub(r'[^\w\s-]', '', pdf_name)[:31]  # Excel limita a 31 caracteres
                    
                    # Datos de tablas
                    table_data = []
                    
                    for key, value in result.items():
                        if key.startswith('tabla_') and isinstance(value, list):
                            # Convertir tabla a DataFrame
                            if value:
                                # Usar primera fila como headers si parece apropiado
                                if len(value) > 1 and all(isinstance(cell, str) for cell in value[0]):
                                    df_table = pd.DataFrame(value[1:], columns=value[0])
                                else:
                                    df_table = pd.DataFrame(value)
                                
                                # Agregar a la hoja
                                if table_data:
                                    # Agregar separador
                                    table_data.append([''] * len(df_table.columns))
                                    table_data.append([f'=== {key.upper()} ==='] + [''] * (len(df_table.columns) - 1))
                                
                                table_data.extend(df_table.values.tolist())
                                if not table_data:  # Primera tabla, agregar headers
                                    table_data = [df_table.columns.tolist()] + df_table.values.tolist()
                    
                    # Si hay datos de tabla, crear hoja
                    if table_data:
                        df_sheet = pd.DataFrame(table_data)
                        df_sheet.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
                    
                    # Si hay contenido de texto estructurado
                    elif 'contenido_texto' in result:
                        content = result['contenido_texto']
                        if isinstance(content, dict):
                            # Convertir diccionario a DataFrame
                            df_content = pd.DataFrame(list(content.items()), columns=['Campo', 'Valor'])
                            df_content.to_excel(writer, sheet_name=sheet_name, index=False)
                        else:
                            # Texto plano
                            df_content = pd.DataFrame([['Contenido', str(content)]], columns=['Campo', 'Valor'])
                            df_content.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"✓ Resultados exportados exitosamente a {filename}")
            
        except Exception as e:
            print(f"✗ Error exportando a Excel: {str(e)}")

def main():
    """
    Función principal
    """
    processor = PDFQRProcessorOpenCV()
    
    # Procesar todos los PDFs
    processor.process_all_pdfs()
    
    # Exportar resultados
    processor.export_to_excel()
    
    print(f"\n=== Procesamiento completado ===")
    print(f"Total de archivos procesados: {len(processor.results)}")

if __name__ == "__main__":
    main()
