@echo off
echo Probando scraper avanzado con multiples estrategias SSL...

call venv\Scripts\activate.bat
python pdf_qr_scraper_advanced.py

echo.
echo Verificando resultados del scraping...
if exist "resultados_scraping_sat.xlsx" (
    echo ✓ Archivo de scraping generado!
    echo ¿Abrir archivo de resultados? (S/N)
    set /p open_file="Respuesta: "
    if /i "%open_file%"=="S" (
        start "" "resultados_scraping_sat.xlsx"
    )
) else (
    echo ✗ No se generó el archivo de scraping
)

pause
