@echo off
title Extractor de QR de PDFs
echo.
echo ========================================
echo    EXTRACTOR DE CODIGOS QR DE PDFs
echo ========================================
echo.

REM Verificar si Python está disponible
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python no está instalado o no está en el PATH.
    echo.
    echo Soluciones:
    echo 1. Instala Python desde https://www.python.org/downloads/
    echo 2. Asegurate de marcar "Add Python to PATH" durante la instalacion
    echo 3. Reinicia la computadora despues de instalar
    echo.
    pause
    exit /b 1
)

REM Verificar si existen PDFs
set pdf_count=0
for %%f in (*.pdf) do set /a pdf_count+=1

if %pdf_count% equ 0 (
    echo ADVERTENCIA: No se encontraron archivos PDF en esta carpeta.
    echo.
    echo Coloca los archivos PDF que quieres procesar en esta misma carpeta.
    echo.
    pause
    exit /b 1
)

echo Archivos PDF encontrados: %pdf_count%
echo.

REM Preguntar si hacer prueba primero
echo ¿Quieres hacer una prueba con un solo PDF primero? (recomendado)
echo.
echo 1. Si - Hacer prueba primero
echo 2. No - Procesar todos los PDFs directamente
echo.
set /p choice="Elige una opcion (1 o 2): "

if "%choice%"=="1" (
    echo.
    echo Ejecutando prueba...
    python test_single_pdf.py
    echo.
    echo ¿Continuar con el procesamiento completo? (S/N)
    set /p continue="Respuesta: "
    if /i not "%continue%"=="S" (
        echo Operacion cancelada.
        pause
        exit /b 0
    )
)

echo.
echo Iniciando procesamiento completo...
echo.
python pdf_qr_extractor.py

echo.
echo ========================================
echo           PROCESAMIENTO COMPLETO
echo ========================================
echo.

REM Verificar si se generó el archivo de resultados
if exist "resultados_qr_extraction.xlsx" (
    echo ✓ Archivo de resultados generado: resultados_qr_extraction.xlsx
    echo.
    echo ¿Quieres abrir el archivo de resultados? (S/N)
    set /p open_file="Respuesta: "
    if /i "%open_file%"=="S" (
        start "" "resultados_qr_extraction.xlsx"
    )
) else (
    echo ⚠ No se generó el archivo de resultados.
    echo Revisa los mensajes de error arriba.
)

echo.
pause
