#!/usr/bin/env python3
"""
Script simple para verificar que Python funciona y las dependencias están disponibles
"""

import sys
import os

def test_python():
    print("=== Verificación de Python ===")
    print(f"✓ Python {sys.version}")
    print(f"✓ Ejecutable: {sys.executable}")
    print(f"✓ Directorio actual: {os.getcwd()}")
    
    # Verificar archivos PDF
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]
    print(f"✓ Archivos PDF encontrados: {len(pdf_files)}")
    
    if pdf_files:
        for i, pdf in enumerate(pdf_files[:5], 1):  # Mostrar solo los primeros 5
            print(f"  {i}. {pdf}")
        if len(pdf_files) > 5:
            print(f"  ... y {len(pdf_files) - 5} más")
    
    return True

def test_dependencies():
    print("\n=== Verificación de Dependencias ===")
    
    dependencies = [
        ('fitz', 'PyMuPDF'),
        ('PIL', 'Pillow'),
        ('cv2', 'opencv-python'),
        ('pyzbar', 'pyzbar'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('requests', 'requests'),
        ('bs4', 'beautifulsoup4'),
        ('numpy', 'numpy')
    ]
    
    missing = []
    
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - NO INSTALADO")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Faltan dependencias: {', '.join(missing)}")
        print("💡 Ejecuta: python -m pip install " + " ".join(missing))
        return False
    else:
        print("\n✅ Todas las dependencias están instaladas!")
        return True

def main():
    print("🔍 Verificando configuración del sistema...\n")
    
    # Verificar Python
    python_ok = test_python()
    
    # Verificar dependencias
    deps_ok = test_dependencies()
    
    print("\n" + "="*50)
    if python_ok and deps_ok:
        print("🎉 ¡Todo está listo!")
        print("✨ Puedes ejecutar el script principal.")
    else:
        print("⚠️  Hay problemas que resolver.")
        if not deps_ok:
            print("🔧 Instala las dependencias faltantes primero.")
    
    print("="*50)

if __name__ == "__main__":
    main()
