@echo off
echo Ejecutando procesamiento completo...

call venv\Scripts\activate.bat
python pdf_qr_extractor_opencv.py

echo.
echo Verificando resultados...
if exist "resultados_qr_extraction.xlsx" (
    echo ✓ Archivo de resultados generado!
    echo ¿Abrir archivo? (S/N)
    set /p open_file="Respuesta: "
    if /i "%open_file%"=="S" (
        start "" "resultados_qr_extraction.xlsx"
    )
) else (
    echo ✗ No se generó archivo de resultados
)

pause
